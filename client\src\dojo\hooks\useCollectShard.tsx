import { useState, useCallback } from "react";
import { useAccount } from "@starknet-react/core";
import { Position } from "../models.gen";
import { dojoConfig } from "../dojoConfig";

interface UseCollectShardReturn {
  collectShard: (position: Position) => Promise<void>;
  isLoading: boolean;
  error: Error | null;
  resetError: () => void;
}

export const useCollectShard = (): UseCollectShardReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const { account } = useAccount();

  const collectShard = useCallback(async (position: Position) => {
    if (!account) {
      setError(new Error("No account connected"));
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${dojoConfig.toriiUrl}/graphql`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: `
            mutation CollectShard($position: Position!) {
              collectShard(position: $position)
            }
          `,
          variables: {
            position: {
              x: position.x.toString(),
              y: position.y.toString(),
              location_id: position.location_id.toString(),
            },
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      console.log("✅ Shard collected successfully:", result.data);
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Failed to collect shard");
      console.error("❌ Error collecting shard:", error);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [account]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    collectShard,
    isLoading,
    error,
    resetError,
  };
}; 