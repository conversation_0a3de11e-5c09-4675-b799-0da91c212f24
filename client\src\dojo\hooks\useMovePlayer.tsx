import { useState, useCallback } from "react";
import { useAccount } from "@starknet-react/core";
import { BigNumberish } from "starknet";
import { dojoConfig } from "../dojoConfig";

interface UseMovePlayerReturn {
  movePlayer: (xDelta: BigNumberish, yDelta: BigNumberish) => Promise<void>;
  isLoading: boolean;
  error: Error | null;
  resetError: () => void;
}

export const useMovePlayer = (): UseMovePlayerReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const { account } = useAccount();

  const movePlayer = useCallback(async (xDelta: BigNumberish, yDelta: BigNumberish) => {
    if (!account) {
      setError(new Error("No account connected"));
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${dojoConfig.toriiUrl}/graphql`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: `
            mutation MovePlayer($xDelta: BigNumberish!, $yDelta: BigNumberish!) {
              movePlayer(xDelta: $xDelta, yDelta: $yDelta)
            }
          `,
          variables: {
            xDelta: xDelta.toString(),
            yDelta: yDelta.toString(),
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      console.log("✅ Player moved successfully:", result.data);
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Failed to move player");
      console.error("❌ Error moving player:", error);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [account]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    movePlayer,
    isLoading,
    error,
    resetError,
  };
}; 