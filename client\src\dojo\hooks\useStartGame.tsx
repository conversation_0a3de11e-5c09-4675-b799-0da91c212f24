import { useState, useCallback } from "react";
import { useAccount } from "@starknet-react/core";
import { dojoConfig } from "../dojoConfig";

interface UseStartGameReturn {
  startGame: () => Promise<void>;
  isLoading: boolean;
  error: Error | null;
  resetError: () => void;
}

export const useStartGame = (): UseStartGameReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const { account } = useAccount();

  const startGame = useCallback(async () => {
    if (!account) {
      setError(new Error("No account connected"));
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${dojoConfig.toriiUrl}/graphql`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: `
            mutation StartGame {
              startGame
            }
          `,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      console.log("✅ Game started successfully:", result.data);
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Failed to start game");
      console.error("❌ Error starting game:", error);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [account]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    startGame,
    isLoading,
    error,
    resetError,
  };
}; 