import { useState, useCallback } from "react";
import { useAccount } from "@starknet-react/core";
import { BigNumberish } from "starknet";
import { dojoConfig } from "../dojoConfig";

interface UseOpenDoorReturn {
  openDoor: (doorId: BigNumberish) => Promise<void>;
  isLoading: boolean;
  error: Error | null;
  resetError: () => void;
}

export const useOpenDoor = (): UseOpenDoorReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const { account } = useAccount();

  const openDoor = useCallback(async (doorId: BigNumberish) => {
    if (!account) {
      setError(new Error("No account connected"));
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${dojoConfig.toriiUrl}/graphql`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: `
            mutation OpenDoor($doorId: BigNumberish!) {
              openDoor(doorId: $doorId)
            }
          `,
          variables: {
            doorId: doorId.toString(),
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      console.log("✅ Door opened successfully:", result.data);
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Failed to open door");
      console.error("❌ Error opening door:", error);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [account]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    openDoor,
    isLoading,
    error,
    resetError,
  };
}; 