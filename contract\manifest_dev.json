{"world": {"class_hash": "0x4c60dc46a8ca8bb47675b7b914053cef769afbf0e340523187336b72bd71d1f", "address": "0x76eba98d1dd58caa8e1b4b0ed11173b53cc5b7abe45b6198dae2e9c5f4c3b63", "seed": "blockrooms1", "name": "blockrooms", "entrypoints": ["uuid", "set_metadata", "register_namespace", "register_event", "register_model", "register_contract", "register_library", "init_contract", "upgrade_event", "upgrade_model", "upgrade_contract", "emit_event", "emit_events", "set_entity", "set_entities", "delete_entity", "delete_entities", "grant_owner", "revoke_owner", "grant_writer", "revoke_writer", "upgrade"], "abi": [{"type": "impl", "name": "World", "interface_name": "dojo::world::iworld::IWorld"}, {"type": "struct", "name": "core::byte_array::ByteArray", "members": [{"name": "data", "type": "core::array::Array::<core::bytes_31::bytes31>"}, {"name": "pending_word", "type": "core::felt252"}, {"name": "pending_word_len", "type": "core::integer::u32"}]}, {"type": "enum", "name": "dojo::world::resource::Resource", "variants": [{"name": "Model", "type": "(core::starknet::contract_address::ContractAddress, core::felt252)"}, {"name": "Event", "type": "(core::starknet::contract_address::ContractAddress, core::felt252)"}, {"name": "Contract", "type": "(core::starknet::contract_address::ContractAddress, core::felt252)"}, {"name": "Namespace", "type": "core::byte_array::ByteArray"}, {"name": "World", "type": "()"}, {"name": "Unregistered", "type": "()"}, {"name": "Library", "type": "(core::starknet::class_hash::ClassHash, core::felt252)"}]}, {"type": "struct", "name": "dojo::model::metadata::ResourceMetadata", "members": [{"name": "resource_id", "type": "core::felt252"}, {"name": "metadata_uri", "type": "core::byte_array::ByteArray"}, {"name": "metadata_hash", "type": "core::felt252"}]}, {"type": "struct", "name": "core::array::Span::<core::felt252>", "members": [{"name": "snapshot", "type": "@core::array::Array::<core::felt252>"}]}, {"type": "struct", "name": "core::array::Span::<core::array::Span::<core::felt252>>", "members": [{"name": "snapshot", "type": "@core::array::Array::<core::array::Span::<core::felt252>>"}]}, {"type": "enum", "name": "dojo::model::definition::ModelIndex", "variants": [{"name": "Keys", "type": "core::array::Span::<core::felt252>"}, {"name": "Id", "type": "core::felt252"}, {"name": "MemberId", "type": "(core::felt252, core::felt252)"}]}, {"type": "struct", "name": "core::array::Span::<core::integer::u8>", "members": [{"name": "snapshot", "type": "@core::array::Array::<core::integer::u8>"}]}, {"type": "struct", "name": "dojo::meta::layout::FieldLayout", "members": [{"name": "selector", "type": "core::felt252"}, {"name": "layout", "type": "dojo::meta::layout::Layout"}]}, {"type": "struct", "name": "core::array::Span::<dojo::meta::layout::FieldLayout>", "members": [{"name": "snapshot", "type": "@core::array::Array::<dojo::meta::layout::FieldLayout>"}]}, {"type": "struct", "name": "core::array::Span::<dojo::meta::layout::Layout>", "members": [{"name": "snapshot", "type": "@core::array::Array::<dojo::meta::layout::Layout>"}]}, {"type": "enum", "name": "dojo::meta::layout::Layout", "variants": [{"name": "Fixed", "type": "core::array::Span::<core::integer::u8>"}, {"name": "Struct", "type": "core::array::Span::<dojo::meta::layout::FieldLayout>"}, {"name": "<PERSON><PERSON>", "type": "core::array::Span::<dojo::meta::layout::Layout>"}, {"name": "Array", "type": "core::array::Span::<dojo::meta::layout::Layout>"}, {"name": "ByteArray", "type": "()"}, {"name": "Enum", "type": "core::array::Span::<dojo::meta::layout::FieldLayout>"}]}, {"type": "struct", "name": "core::array::Span::<dojo::model::definition::ModelIndex>", "members": [{"name": "snapshot", "type": "@core::array::Array::<dojo::model::definition::ModelIndex>"}]}, {"type": "enum", "name": "core::bool", "variants": [{"name": "False", "type": "()"}, {"name": "True", "type": "()"}]}, {"type": "interface", "name": "dojo::world::iworld::IWorld", "items": [{"type": "function", "name": "resource", "inputs": [{"name": "selector", "type": "core::felt252"}], "outputs": [{"type": "dojo::world::resource::Resource"}], "state_mutability": "view"}, {"type": "function", "name": "uuid", "inputs": [], "outputs": [{"type": "core::integer::u32"}], "state_mutability": "external"}, {"type": "function", "name": "metadata", "inputs": [{"name": "resource_selector", "type": "core::felt252"}], "outputs": [{"type": "dojo::model::metadata::ResourceMetadata"}], "state_mutability": "view"}, {"type": "function", "name": "set_metadata", "inputs": [{"name": "metadata", "type": "dojo::model::metadata::ResourceMetadata"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "register_namespace", "inputs": [{"name": "namespace", "type": "core::byte_array::ByteArray"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "register_event", "inputs": [{"name": "namespace", "type": "core::byte_array::ByteArray"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "register_model", "inputs": [{"name": "namespace", "type": "core::byte_array::ByteArray"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "register_contract", "inputs": [{"name": "salt", "type": "core::felt252"}, {"name": "namespace", "type": "core::byte_array::ByteArray"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash"}], "outputs": [{"type": "core::starknet::contract_address::ContractAddress"}], "state_mutability": "external"}, {"type": "function", "name": "register_library", "inputs": [{"name": "namespace", "type": "core::byte_array::ByteArray"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash"}, {"name": "name", "type": "core::byte_array::ByteArray"}, {"name": "version", "type": "core::byte_array::ByteArray"}], "outputs": [{"type": "core::starknet::class_hash::ClassHash"}], "state_mutability": "external"}, {"type": "function", "name": "init_contract", "inputs": [{"name": "selector", "type": "core::felt252"}, {"name": "init_calldata", "type": "core::array::Span::<core::felt252>"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "upgrade_event", "inputs": [{"name": "namespace", "type": "core::byte_array::ByteArray"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "upgrade_model", "inputs": [{"name": "namespace", "type": "core::byte_array::ByteArray"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "upgrade_contract", "inputs": [{"name": "namespace", "type": "core::byte_array::ByteArray"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash"}], "outputs": [{"type": "core::starknet::class_hash::ClassHash"}], "state_mutability": "external"}, {"type": "function", "name": "emit_event", "inputs": [{"name": "event_selector", "type": "core::felt252"}, {"name": "keys", "type": "core::array::Span::<core::felt252>"}, {"name": "values", "type": "core::array::Span::<core::felt252>"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "emit_events", "inputs": [{"name": "event_selector", "type": "core::felt252"}, {"name": "keys", "type": "core::array::Span::<core::array::Span::<core::felt252>>"}, {"name": "values", "type": "core::array::Span::<core::array::Span::<core::felt252>>"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "entity", "inputs": [{"name": "model_selector", "type": "core::felt252"}, {"name": "index", "type": "dojo::model::definition::ModelIndex"}, {"name": "layout", "type": "dojo::meta::layout::Layout"}], "outputs": [{"type": "core::array::Span::<core::felt252>"}], "state_mutability": "view"}, {"type": "function", "name": "entities", "inputs": [{"name": "model_selector", "type": "core::felt252"}, {"name": "indexes", "type": "core::array::Span::<dojo::model::definition::ModelIndex>"}, {"name": "layout", "type": "dojo::meta::layout::Layout"}], "outputs": [{"type": "core::array::Span::<core::array::Span::<core::felt252>>"}], "state_mutability": "view"}, {"type": "function", "name": "set_entity", "inputs": [{"name": "model_selector", "type": "core::felt252"}, {"name": "index", "type": "dojo::model::definition::ModelIndex"}, {"name": "values", "type": "core::array::Span::<core::felt252>"}, {"name": "layout", "type": "dojo::meta::layout::Layout"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "set_entities", "inputs": [{"name": "model_selector", "type": "core::felt252"}, {"name": "indexes", "type": "core::array::Span::<dojo::model::definition::ModelIndex>"}, {"name": "values", "type": "core::array::Span::<core::array::Span::<core::felt252>>"}, {"name": "layout", "type": "dojo::meta::layout::Layout"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "delete_entity", "inputs": [{"name": "model_selector", "type": "core::felt252"}, {"name": "index", "type": "dojo::model::definition::ModelIndex"}, {"name": "layout", "type": "dojo::meta::layout::Layout"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "delete_entities", "inputs": [{"name": "model_selector", "type": "core::felt252"}, {"name": "indexes", "type": "core::array::Span::<dojo::model::definition::ModelIndex>"}, {"name": "layout", "type": "dojo::meta::layout::Layout"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "is_owner", "inputs": [{"name": "resource", "type": "core::felt252"}, {"name": "address", "type": "core::starknet::contract_address::ContractAddress"}], "outputs": [{"type": "core::bool"}], "state_mutability": "view"}, {"type": "function", "name": "grant_owner", "inputs": [{"name": "resource", "type": "core::felt252"}, {"name": "address", "type": "core::starknet::contract_address::ContractAddress"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "revoke_owner", "inputs": [{"name": "resource", "type": "core::felt252"}, {"name": "address", "type": "core::starknet::contract_address::ContractAddress"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "owners_count", "inputs": [{"name": "resource", "type": "core::felt252"}], "outputs": [{"type": "core::integer::u64"}], "state_mutability": "view"}, {"type": "function", "name": "is_writer", "inputs": [{"name": "resource", "type": "core::felt252"}, {"name": "contract", "type": "core::starknet::contract_address::ContractAddress"}], "outputs": [{"type": "core::bool"}], "state_mutability": "view"}, {"type": "function", "name": "grant_writer", "inputs": [{"name": "resource", "type": "core::felt252"}, {"name": "contract", "type": "core::starknet::contract_address::ContractAddress"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "revoke_writer", "inputs": [{"name": "resource", "type": "core::felt252"}, {"name": "contract", "type": "core::starknet::contract_address::ContractAddress"}], "outputs": [], "state_mutability": "external"}]}, {"type": "impl", "name": "UpgradeableWorld", "interface_name": "dojo::world::iworld::IUpgradeableWorld"}, {"type": "interface", "name": "dojo::world::iworld::IUpgradeableWorld", "items": [{"type": "function", "name": "upgrade", "inputs": [{"name": "new_class_hash", "type": "core::starknet::class_hash::ClassHash"}], "outputs": [], "state_mutability": "external"}]}, {"type": "constructor", "name": "constructor", "inputs": [{"name": "world_class_hash", "type": "core::starknet::class_hash::ClassHash"}]}, {"type": "event", "name": "dojo::world::world_contract::world::WorldSpawned", "kind": "struct", "members": [{"name": "creator", "type": "core::starknet::contract_address::ContractAddress", "kind": "data"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::WorldUpgraded", "kind": "struct", "members": [{"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::NamespaceRegistered", "kind": "struct", "members": [{"name": "namespace", "type": "core::byte_array::ByteArray", "kind": "key"}, {"name": "hash", "type": "core::felt252", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::ModelRegistered", "kind": "struct", "members": [{"name": "name", "type": "core::byte_array::ByteArray", "kind": "key"}, {"name": "namespace", "type": "core::byte_array::ByteArray", "kind": "key"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}, {"name": "address", "type": "core::starknet::contract_address::ContractAddress", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::EventRegistered", "kind": "struct", "members": [{"name": "name", "type": "core::byte_array::ByteArray", "kind": "key"}, {"name": "namespace", "type": "core::byte_array::ByteArray", "kind": "key"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}, {"name": "address", "type": "core::starknet::contract_address::ContractAddress", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::ContractRegistered", "kind": "struct", "members": [{"name": "name", "type": "core::byte_array::ByteArray", "kind": "key"}, {"name": "namespace", "type": "core::byte_array::ByteArray", "kind": "key"}, {"name": "address", "type": "core::starknet::contract_address::ContractAddress", "kind": "data"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}, {"name": "salt", "type": "core::felt252", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::ModelUpgraded", "kind": "struct", "members": [{"name": "selector", "type": "core::felt252", "kind": "key"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}, {"name": "address", "type": "core::starknet::contract_address::ContractAddress", "kind": "data"}, {"name": "prev_address", "type": "core::starknet::contract_address::ContractAddress", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::EventUpgraded", "kind": "struct", "members": [{"name": "selector", "type": "core::felt252", "kind": "key"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}, {"name": "address", "type": "core::starknet::contract_address::ContractAddress", "kind": "data"}, {"name": "prev_address", "type": "core::starknet::contract_address::ContractAddress", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::ContractUpgraded", "kind": "struct", "members": [{"name": "selector", "type": "core::felt252", "kind": "key"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::ContractInitialized", "kind": "struct", "members": [{"name": "selector", "type": "core::felt252", "kind": "key"}, {"name": "init_calldata", "type": "core::array::Span::<core::felt252>", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::LibraryRegistered", "kind": "struct", "members": [{"name": "name", "type": "core::byte_array::ByteArray", "kind": "key"}, {"name": "namespace", "type": "core::byte_array::ByteArray", "kind": "key"}, {"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::EventEmitted", "kind": "struct", "members": [{"name": "selector", "type": "core::felt252", "kind": "key"}, {"name": "system_address", "type": "core::starknet::contract_address::ContractAddress", "kind": "key"}, {"name": "keys", "type": "core::array::Span::<core::felt252>", "kind": "data"}, {"name": "values", "type": "core::array::Span::<core::felt252>", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::MetadataUpdate", "kind": "struct", "members": [{"name": "resource", "type": "core::felt252", "kind": "key"}, {"name": "uri", "type": "core::byte_array::ByteArray", "kind": "data"}, {"name": "hash", "type": "core::felt252", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::StoreSetRecord", "kind": "struct", "members": [{"name": "selector", "type": "core::felt252", "kind": "key"}, {"name": "entity_id", "type": "core::felt252", "kind": "key"}, {"name": "keys", "type": "core::array::Span::<core::felt252>", "kind": "data"}, {"name": "values", "type": "core::array::Span::<core::felt252>", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::StoreUpdateRecord", "kind": "struct", "members": [{"name": "selector", "type": "core::felt252", "kind": "key"}, {"name": "entity_id", "type": "core::felt252", "kind": "key"}, {"name": "values", "type": "core::array::Span::<core::felt252>", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::StoreUpdateMember", "kind": "struct", "members": [{"name": "selector", "type": "core::felt252", "kind": "key"}, {"name": "entity_id", "type": "core::felt252", "kind": "key"}, {"name": "member_selector", "type": "core::felt252", "kind": "key"}, {"name": "values", "type": "core::array::Span::<core::felt252>", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::StoreDelRecord", "kind": "struct", "members": [{"name": "selector", "type": "core::felt252", "kind": "key"}, {"name": "entity_id", "type": "core::felt252", "kind": "key"}]}, {"type": "event", "name": "dojo::world::world_contract::world::WriterUpdated", "kind": "struct", "members": [{"name": "resource", "type": "core::felt252", "kind": "key"}, {"name": "contract", "type": "core::starknet::contract_address::ContractAddress", "kind": "key"}, {"name": "value", "type": "core::bool", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::OwnerUpdated", "kind": "struct", "members": [{"name": "resource", "type": "core::felt252", "kind": "key"}, {"name": "contract", "type": "core::starknet::contract_address::ContractAddress", "kind": "key"}, {"name": "value", "type": "core::bool", "kind": "data"}]}, {"type": "event", "name": "dojo::world::world_contract::world::Event", "kind": "enum", "variants": [{"name": "WorldSpawned", "type": "dojo::world::world_contract::world::WorldSpawned", "kind": "nested"}, {"name": "WorldUpgraded", "type": "dojo::world::world_contract::world::WorldUpgraded", "kind": "nested"}, {"name": "NamespaceRegistered", "type": "dojo::world::world_contract::world::NamespaceRegistered", "kind": "nested"}, {"name": "ModelRegistered", "type": "dojo::world::world_contract::world::ModelRegistered", "kind": "nested"}, {"name": "EventRegistered", "type": "dojo::world::world_contract::world::EventRegistered", "kind": "nested"}, {"name": "ContractRegistered", "type": "dojo::world::world_contract::world::ContractRegistered", "kind": "nested"}, {"name": "ModelUpgraded", "type": "dojo::world::world_contract::world::ModelUpgraded", "kind": "nested"}, {"name": "EventUpgraded", "type": "dojo::world::world_contract::world::EventUpgraded", "kind": "nested"}, {"name": "ContractUpgraded", "type": "dojo::world::world_contract::world::ContractUpgraded", "kind": "nested"}, {"name": "ContractInitialized", "type": "dojo::world::world_contract::world::ContractInitialized", "kind": "nested"}, {"name": "LibraryRegistered", "type": "dojo::world::world_contract::world::LibraryRegistered", "kind": "nested"}, {"name": "EventEmitted", "type": "dojo::world::world_contract::world::EventEmitted", "kind": "nested"}, {"name": "MetadataUpdate", "type": "dojo::world::world_contract::world::MetadataUpdate", "kind": "nested"}, {"name": "StoreSetRecord", "type": "dojo::world::world_contract::world::StoreSetRecord", "kind": "nested"}, {"name": "StoreUpdateRecord", "type": "dojo::world::world_contract::world::StoreUpdateRecord", "kind": "nested"}, {"name": "StoreUpdateMember", "type": "dojo::world::world_contract::world::StoreUpdateMember", "kind": "nested"}, {"name": "StoreDelRecord", "type": "dojo::world::world_contract::world::StoreDelRecord", "kind": "nested"}, {"name": "WriterUpdated", "type": "dojo::world::world_contract::world::WriterUpdated", "kind": "nested"}, {"name": "OwnerUpdated", "type": "dojo::world::world_contract::world::OwnerUpdated", "kind": "nested"}]}]}, "contracts": [{"address": "0xe63339b4b65057a644c0c7e9d7863ef6eb83fa4c33d59e637646b0075e9765", "class_hash": "0x23365ba311bdaa8803429684863caed754b198e7345f344e3d4b472eee30953", "abi": [{"type": "impl", "name": "actions__ContractImpl", "interface_name": "dojo::contract::interface::IContract"}, {"type": "interface", "name": "dojo::contract::interface::IContract", "items": []}, {"type": "impl", "name": "actions__DeployedContractImpl", "interface_name": "dojo::meta::interface::IDeployedResource"}, {"type": "struct", "name": "core::byte_array::ByteArray", "members": [{"name": "data", "type": "core::array::Array::<core::bytes_31::bytes31>"}, {"name": "pending_word", "type": "core::felt252"}, {"name": "pending_word_len", "type": "core::integer::u32"}]}, {"type": "interface", "name": "dojo::meta::interface::IDeployedResource", "items": [{"type": "function", "name": "dojo_name", "inputs": [], "outputs": [{"type": "core::byte_array::ByteArray"}], "state_mutability": "view"}]}, {"type": "impl", "name": "BlockRoomsImpl", "interface_name": "blockrooms::systems::actions::IBlockRooms"}, {"type": "struct", "name": "blockrooms::models::MoveAction", "members": [{"name": "x_delta", "type": "core::integer::i32"}, {"name": "y_delta", "type": "core::integer::i32"}]}, {"type": "struct", "name": "blockrooms::models::OpenDoorAction", "members": [{"name": "door_id", "type": "core::integer::u32"}]}, {"type": "struct", "name": "blockrooms::models::AttackAction", "members": [{"name": "entity_id", "type": "core::felt252"}]}, {"type": "struct", "name": "blockrooms::models::Position", "members": [{"name": "x", "type": "core::integer::u32"}, {"name": "y", "type": "core::integer::u32"}, {"name": "location_id", "type": "core::integer::u32"}]}, {"type": "struct", "name": "blockrooms::models::CollectShardAction", "members": [{"name": "action_id", "type": "core::felt252"}, {"name": "position", "type": "blockrooms::models::Position"}]}, {"type": "enum", "name": "blockrooms::models::Action", "variants": [{"name": "Move", "type": "blockrooms::models::MoveAction"}, {"name": "OpenDoor", "type": "blockrooms::models::OpenDoorAction"}, {"name": "Attack", "type": "blockrooms::models::AttackAction"}, {"name": "CollectShard", "type": "blockrooms::models::CollectShardAction"}]}, {"type": "enum", "name": "blockrooms::models::ActionType", "variants": [{"name": "Move", "type": "()"}, {"name": "OpenDoor", "type": "()"}, {"name": "Attack", "type": "()"}, {"name": "CollectShard", "type": "()"}]}, {"type": "enum", "name": "core::bool", "variants": [{"name": "False", "type": "()"}, {"name": "True", "type": "()"}]}, {"type": "struct", "name": "blockrooms::models::ActionValidation", "members": [{"name": "validation_id", "type": "core::felt252"}, {"name": "player_id", "type": "core::starknet::contract_address::ContractAddress"}, {"name": "action_index", "type": "core::integer::u32"}, {"name": "action_type", "type": "blockrooms::models::ActionType"}, {"name": "is_valid", "type": "core::bool"}, {"name": "error_reason", "type": "core::felt252"}, {"name": "required_shards", "type": "core::integer::u32"}, {"name": "required_health", "type": "core::integer::u32"}, {"name": "required_position", "type": "blockrooms::models::Position"}]}, {"type": "enum", "name": "core::option::Option::<core::integer::u32>", "variants": [{"name": "Some", "type": "core::integer::u32"}, {"name": "None", "type": "()"}]}, {"type": "struct", "name": "blockrooms::models::Player", "members": [{"name": "player_id", "type": "core::starknet::contract_address::ContractAddress"}, {"name": "position", "type": "blockrooms::models::Position"}, {"name": "health", "type": "core::integer::u32"}, {"name": "max_health", "type": "core::integer::u32"}, {"name": "shards", "type": "core::integer::u32"}, {"name": "game_active", "type": "core::bool"}, {"name": "is_alive", "type": "core::bool"}, {"name": "current_session_id", "type": "core::felt252"}, {"name": "rooms_cleared", "type": "core::integer::u32"}, {"name": "turn_number", "type": "core::integer::u32"}, {"name": "dodge_active_turns", "type": "core::integer::u32"}, {"name": "has_shard_one", "type": "core::bool"}, {"name": "has_shard_two", "type": "core::bool"}, {"name": "has_shard_three", "type": "core::bool"}, {"name": "entered_door_id", "type": "core::option::Option::<core::integer::u32>"}, {"name": "door_enemy_alive", "type": "core::bool"}, {"name": "movement_locked", "type": "core::bool"}, {"name": "special_ability_cooldown", "type": "core::integer::u32"}, {"name": "has_key", "type": "core::bool"}]}, {"type": "struct", "name": "blockrooms::models::GridBounds", "members": [{"name": "min_x", "type": "core::integer::u32"}, {"name": "max_x", "type": "core::integer::u32"}, {"name": "min_y", "type": "core::integer::u32"}, {"name": "max_y", "type": "core::integer::u32"}]}, {"type": "struct", "name": "blockrooms::models::Room", "members": [{"name": "room_id", "type": "core::integer::u32"}, {"name": "initialized", "type": "core::bool"}, {"name": "cleared", "type": "core::bool"}, {"name": "entity_count", "type": "core::integer::u32"}, {"name": "active_entities", "type": "core::integer::u32"}, {"name": "has_treasure", "type": "core::bool"}, {"name": "treasure_collected", "type": "core::bool"}, {"name": "door_count", "type": "core::integer::u32"}, {"name": "boundaries", "type": "blockrooms::models::GridBounds"}]}, {"type": "enum", "name": "blockrooms::models::EntityType", "variants": [{"name": "Male", "type": "()"}, {"name": "Female", "type": "()"}]}, {"type": "enum", "name": "blockrooms::models::NumberedShard", "variants": [{"name": "One", "type": "()"}, {"name": "Two", "type": "()"}, {"name": "Three", "type": "()"}]}, {"type": "enum", "name": "core::option::Option::<blockrooms::models::NumberedShard>", "variants": [{"name": "Some", "type": "blockrooms::models::NumberedShard"}, {"name": "None", "type": "()"}]}, {"type": "struct", "name": "blockrooms::models::Entity", "members": [{"name": "entity_id", "type": "core::felt252"}, {"name": "entity_type", "type": "blockrooms::models::EntityType"}, {"name": "position", "type": "blockrooms::models::Position"}, {"name": "health", "type": "core::integer::u32"}, {"name": "max_health", "type": "core::integer::u32"}, {"name": "is_alive", "type": "core::bool"}, {"name": "damage_per_turn", "type": "core::integer::u32"}, {"name": "drops_numbered_shard", "type": "core::option::Option::<blockrooms::models::NumberedShard>"}, {"name": "spawned_from_door", "type": "core::bool"}]}, {"type": "struct", "name": "blockrooms::models::Doorway", "members": [{"name": "doorway_id", "type": "core::integer::u32"}, {"name": "position", "type": "blockrooms::models::Position"}, {"name": "room_id", "type": "core::integer::u32"}, {"name": "connected_room_id", "type": "core::integer::u32"}, {"name": "is_open", "type": "core::bool"}, {"name": "requires_cleared", "type": "core::bool"}]}, {"type": "enum", "name": "blockrooms::models::GameResult", "variants": [{"name": "InProgress", "type": "()"}, {"name": "Victory", "type": "()"}, {"name": "Defeat", "type": "()"}]}, {"type": "struct", "name": "blockrooms::models::TurnExecution", "members": [{"name": "turn_id", "type": "core::felt252"}, {"name": "player_id", "type": "core::starknet::contract_address::ContractAddress"}, {"name": "session_id", "type": "core::felt252"}, {"name": "actions_count", "type": "core::integer::u32"}, {"name": "successful_actions", "type": "core::integer::u32"}, {"name": "total_damage_dealt", "type": "core::integer::u32"}, {"name": "total_damage_taken", "type": "core::integer::u32"}, {"name": "total_shards_gained", "type": "core::integer::u32"}, {"name": "numbered_shards_collected", "type": "core::integer::u32"}, {"name": "timestamp", "type": "core::integer::u64"}, {"name": "turn_number", "type": "core::integer::u32"}]}, {"type": "interface", "name": "blockrooms::systems::actions::IBlockRooms", "items": [{"type": "function", "name": "initialize_player", "inputs": [], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "start_game", "inputs": [], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "respawn_player", "inputs": [], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "execute_turn", "inputs": [{"name": "actions", "type": "core::array::Array::<blockrooms::models::Action>"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "validate_actions", "inputs": [{"name": "actions", "type": "core::array::Array::<blockrooms::models::Action>"}], "outputs": [{"type": "core::array::Array::<blockrooms::models::ActionValidation>"}], "state_mutability": "view"}, {"type": "function", "name": "move_player", "inputs": [{"name": "x_delta", "type": "core::integer::i32"}, {"name": "y_delta", "type": "core::integer::i32"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "attack_entity", "inputs": [{"name": "entity_id", "type": "core::felt252"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "collect_shard", "inputs": [{"name": "position", "type": "blockrooms::models::Position"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "open_door", "inputs": [{"name": "door_id", "type": "core::integer::u32"}], "outputs": [], "state_mutability": "external"}, {"type": "function", "name": "get_player_state", "inputs": [], "outputs": [{"type": "blockrooms::models::Player"}], "state_mutability": "view"}, {"type": "function", "name": "get_room_state", "inputs": [{"name": "room_id", "type": "core::integer::u32"}], "outputs": [{"type": "blockrooms::models::Room"}], "state_mutability": "view"}, {"type": "function", "name": "get_entities_in_location", "inputs": [{"name": "location_id", "type": "core::integer::u32"}], "outputs": [{"type": "core::array::Array::<blockrooms::models::Entity>"}], "state_mutability": "view"}, {"type": "function", "name": "get_available_doorways", "inputs": [{"name": "location_id", "type": "core::integer::u32"}], "outputs": [{"type": "core::array::Array::<blockrooms::models::Doorway>"}], "state_mutability": "view"}, {"type": "function", "name": "get_game_status", "inputs": [], "outputs": [{"type": "blockrooms::models::GameResult"}], "state_mutability": "view"}, {"type": "function", "name": "get_turn_history", "inputs": [{"name": "limit", "type": "core::integer::u32"}], "outputs": [{"type": "core::array::Array::<blockrooms::models::TurnExecution>"}], "state_mutability": "view"}, {"type": "function", "name": "end_game", "inputs": [], "outputs": [], "state_mutability": "external"}]}, {"type": "function", "name": "dojo_init", "inputs": [], "outputs": [], "state_mutability": "view"}, {"type": "impl", "name": "WorldProviderImpl", "interface_name": "dojo::contract::components::world_provider::IWorldProvider"}, {"type": "struct", "name": "dojo::world::iworld::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "members": [{"name": "contract_address", "type": "core::starknet::contract_address::ContractAddress"}]}, {"type": "interface", "name": "dojo::contract::components::world_provider::IWorldProvider", "items": [{"type": "function", "name": "world_dispatcher", "inputs": [], "outputs": [{"type": "dojo::world::iworld::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "state_mutability": "view"}]}, {"type": "impl", "name": "UpgradeableImpl", "interface_name": "dojo::contract::components::upgradeable::IUpgradeable"}, {"type": "interface", "name": "dojo::contract::components::upgradeable::IUpgradeable", "items": [{"type": "function", "name": "upgrade", "inputs": [{"name": "new_class_hash", "type": "core::starknet::class_hash::ClassHash"}], "outputs": [], "state_mutability": "external"}]}, {"type": "constructor", "name": "constructor", "inputs": []}, {"type": "event", "name": "dojo::contract::components::upgradeable::upgradeable_cpt::Upgraded", "kind": "struct", "members": [{"name": "class_hash", "type": "core::starknet::class_hash::ClassHash", "kind": "data"}]}, {"type": "event", "name": "dojo::contract::components::upgradeable::upgradeable_cpt::Event", "kind": "enum", "variants": [{"name": "Upgraded", "type": "dojo::contract::components::upgradeable::upgradeable_cpt::Upgraded", "kind": "nested"}]}, {"type": "event", "name": "dojo::contract::components::world_provider::world_provider_cpt::Event", "kind": "enum", "variants": []}, {"type": "event", "name": "blockrooms::systems::actions::actions::Event", "kind": "enum", "variants": [{"name": "UpgradeableEvent", "type": "dojo::contract::components::upgradeable::upgradeable_cpt::Event", "kind": "nested"}, {"name": "WorldProviderEvent", "type": "dojo::contract::components::world_provider::world_provider_cpt::Event", "kind": "nested"}]}], "init_calldata": [], "tag": "blockrooms-actions", "selector": "0x61254bd76919f4f416c926da2ab20235c225738733258982d08286671d18de1", "systems": ["initialize_player", "start_game", "respawn_player", "execute_turn", "move_player", "attack_entity", "collect_shard", "open_door", "end_game", "upgrade"]}], "libraries": [], "models": [{"members": [], "class_hash": "0x51b487107d2a9fec8a8a2dd37efc79ae87efa84f13e637a935922547cda2ba5", "tag": "blockrooms-Door", "selector": "0x77420242a527d2497f8153ce92376cfc8f6736879eec766c504b580c7b59a1"}, {"members": [], "class_hash": "0x49762aa75b6b6375dc16d293ef8e30fd179e7f93fedd80d518197961ae8491", "tag": "blockrooms-Doorway", "selector": "0x5752de487c6e01c52ca8c8e22521e8df1afaa202ec4482a92898fd7872d4ab8"}, {"members": [], "class_hash": "0x4d89715a42560f8040d29b4e3e8158c5087880d074642e3ac2c229c21605e0c", "tag": "blockrooms-Entity", "selector": "0x1272011998f556eb52985a9e53590089ff39f59ec203e9026f8e2c998d08691"}, {"members": [], "class_hash": "0x44d7be871d56e270cad59df9c6c3e8e84a549446465133a05f6048a875ad4c4", "tag": "blockrooms-EntityState", "selector": "0x35eb370e08f9a1e314ec6c5f5cf25dee4a8046fe65f195e02cdde3ab438495d"}, {"members": [], "class_hash": "0x72a94f7d4915fe96c0ec5e1390ba6df2c4babfb1080841a0116435e6fcc9b55", "tag": "blockrooms-GameConfig", "selector": "0x3a0c8dae2a8414f9434133a848d474f69cf1dd365d6f4df0cd969b113b0c6b5"}, {"members": [], "class_hash": "0x6806468a1304a9e3bd83098b5aabdad0a1f03be21a89a16b4720ab223a19ab5", "tag": "blockrooms-GameSession", "selector": "0x657010e660774d38be04975c9e93606a4f70d3507cf9bb4dcef2861949da77e"}, {"members": [], "class_hash": "0x52a6a24a040e786ddf01925e7ab66a7e760013c8f2bf061c0386d94ad1fe7d0", "tag": "blockrooms-Player", "selector": "0x3fedb36ca2699d734a292ba7770731415743f643b9c0448fd079c3a5dd8c3c6"}, {"members": [], "class_hash": "0x55f5a949d94acca8d723b695e90b9d2788c25636a01b1761ec8ca6c8eceb7e0", "tag": "blockrooms-PlayerStats", "selector": "0x3e548c8a1f1ea80e0965dd36a4e0c5a401b2f4cd139e157370688fbb69eb720"}, {"members": [], "class_hash": "0x548244c5218b70a3d7a4abb8bad5c0319bd3dfc5b3ed0aeb78b90ec26f61471", "tag": "blockrooms-Room", "selector": "0x763ccee7f5f04191c81cd2620007c11dbb55615d667ec6bb4dd1359cbba94b8"}, {"members": [], "class_hash": "0x45c230d9af0f6e60b2b21666e9b0bb5a68afd9a929f6bf68b2911a20cfaa39e", "tag": "blockrooms-ShardLocation", "selector": "0x51ed4cbf3157867a141ce8056487baf81cc75a4d8326af164df593cce4abacf"}, {"members": [], "class_hash": "0x2b290189efaf52d3525b9cee04b7157c43ba964119e50373c91af635274b2e4", "tag": "blockrooms-TurnExecution", "selector": "0x1ce2f6e57403289a5158a7f70f4884924e7265ad365400574482272d41e83ed"}], "events": [{"members": [], "class_hash": "0x565ad049bfb86c1fa856b44016020b0f5584936a71a8e25d598ec3ba298d56c", "tag": "blockrooms-ActionExecuted", "selector": "0x3474c4712170dd49081437828f8987844726b02d48143497c3ad0981fe8c9ea"}, {"members": [], "class_hash": "0x428743aa34299ff577c20b1e2c6b1a93bde5a0b0f15d4bd7585f92077f53eba", "tag": "blockrooms-GameCompleted", "selector": "0x2555f977bb26694b673a2edda760c9651bbf45c9389efdc30f05cf1083e7676"}, {"members": [], "class_hash": "0x677fd745d1429c06838dd802caf6252e967ec0678a0423da10d255bc0b3f6e2", "tag": "blockrooms-GameStarted", "selector": "0x5e3eb2cdf0e7aad02e3f9008701313437620462fa2130622d62550f0faa9206"}, {"members": [], "class_hash": "0x60c5e3529b5f98134e5306a4c0d808d81d633ccc27a0089c268cd9a23904a3f", "tag": "blockrooms-NumberedShardCollected", "selector": "0x21368136dd54582d027c33b9c84bfce1c7760eafbf341d5fb4938549cab5ad2"}, {"members": [], "class_hash": "0x25f13ca449de151be3d9b616ccfb201ca69a17c26a6109ac42f6cceea478947", "tag": "blockrooms-PlayerDeath", "selector": "0x24bd0fd50c1d9c41f00ab48c442d681b701e11d3d5124ecf35391aafa0e3e2e"}, {"members": [], "class_hash": "0x47b3decbd273b35a3c96981e42a5f567cb1a5fb9b6452fb4a0280f95fd71ba9", "tag": "blockrooms-RoomCleared", "selector": "0x4512355f3651aa56536863983b3af8f0fe32944aabc03d2ae584120efc703d8"}, {"members": [], "class_hash": "0x20ae27e84ebf64bd74b8d69ae393994c330ab3b87595dc9f692d4dd8165897a", "tag": "blockrooms-TurnExecuted", "selector": "0x1ac8ea60b37fe4d186db23e8aeac6e0602462af3678add25f0bd4c1ee7c5e11"}, {"members": [], "class_hash": "0x6717bb19f9e5c57053046ac6ed0b7a0e5d2817ceea31d560e7427a4fbe41a44", "tag": "blockrooms-VictoryAchieved", "selector": "0x112183cc3cfea4faa5c62e53d75f6ea6c9d28c50d225d9e35b88fd394b1a2e7"}], "external_contracts": []}