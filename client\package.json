{"name": "dojo-starter-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:https": "VITE_LOCAL_HTTPS=true vite", "dev:http": "VITE_LOCAL_HTTPS=false vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "preview:https": "VITE_LOCAL_HTTPS=true vite preview", "serve": "vite preview", "format:check": "prettier --check .", "format": "prettier --write .", "mkcert": "mkcert -key-file dev-key.pem -cert-file dev.pem localhost 127.0.0.1 ::1"}, "dependencies": {"@cartridge/connector": "^0.7.13", "@cartridge/controller": "^0.7.13", "@dojoengine/core": "^1.5.15", "@dojoengine/create-burner": "^1.5.15", "@dojoengine/predeployed-connector": "^1.5.15", "@dojoengine/sdk": "^1.5.16", "@dojoengine/torii-client": "^1.5.14", "@dojoengine/torii-wasm": "^1.5.14", "@dojoengine/utils": "^1.5.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@starknet-react/chains": "^3.1.3", "@starknet-react/core": "^3.7.4", "@types/uuid": "^10.0.0", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "immer": "^10.1.1", "lucide-react": "^0.518.0", "react": "^18.3.1", "react-dom": "^18.3.1", "starknet": "^6.23.1", "tailwind-merge": "^3.3.1", "three": "^0.158.0", "uuid": "^10.0.0", "vite-plugin-top-level-await": "^1.5.0", "vite-plugin-wasm": "^3.4.1", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^22.15.32", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/three": "^0.177.0", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vite": "^5.4.19"}}