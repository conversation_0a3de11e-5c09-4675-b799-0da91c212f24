// import React from "react";

// export function Instructions(): JSX.Element {
//   return (
//     <div
//       style={{
//         position: "absolute",
//         top: "20px",
//         left: "20px",
//         color: "white",
//         fontFamily: "Arial, sans-serif",
//         fontSize: "14px",
//         background: "rgba(0, 0, 0, 0.7)",
//         padding: "15px",
//         borderRadius: "8px",
//         zIndex: 1000,
//         maxWidth: "300px",
//       }}
//     >
//       <div style={{ marginBottom: "10px", fontWeight: "bold" }}>🎮 Controls</div>
//       <div>• Click to lock mouse cursor</div>
//       <div>• WASD or Arrow Keys to move</div>
//       <div>• Mouse to look around</div>
//       <div>• Left Click to shoot</div>
//       <div>• ESC to unlock cursor</div>
//       <div style={{ marginTop: "10px", fontSize: "12px", opacity: "0.8" }}>
//         🎯 Hunt enemies in the 20 rooms!
//         <br />
//         🔴 Red alerts show enemy locations
//         <br />⚡ New enemy spawns 5-6s after kill
//       </div>
//     </div>
//   );
// }
