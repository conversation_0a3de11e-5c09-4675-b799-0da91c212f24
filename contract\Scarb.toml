[package]
cairo-version = "2.10.1"
name = "blockrooms"
version = "0.1.0"
edition = "2024_07"

[cairo]
sierra-replace-ids = true

[scripts]
sepolia = "sozo --profile sepolia clean && sozo --profile sepolia build && sozo --profile sepolia migrate --account-address $DEPLOYER_ACCOUNT_ADDRESS --private-key $DEPLOYER_PRIVATE_KEY --fee strk"

[dependencies]
dojo = { git = "https://github.com/dojoengine/dojo", tag = "v1.5.0" }
achievement = { git = "https://github.com/cartridge-gg/arcade", branch = "main" }

[[target.starknet-contract]]
build-external-contracts = [
    "dojo::world::world_contract::world"
]

[dev-dependencies]
cairo_test = "2.10.1"
dojo_cairo_test = { git = "https://github.com/dojoengine/dojo", tag = "v1.5.0" }

[profile.sepolia]
