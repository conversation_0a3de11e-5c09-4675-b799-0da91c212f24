import { useState, useCallback, useEffect } from "react";
import { Canvas } from "@react-three/fiber";
import { PointerLockControls } from "@react-three/drei";
import { Vector3 } from "three";
import * as THREE from "three";

import { useAppStore } from "../zustand/store";

import { WarningDialog } from "../components/ui/WarningDialog";

import { Crosshair } from "../components/ui/Crosshair";
// import { Instructions } from "../components/ui/Instructions";
import { MapTracker } from "../components/systems/MapTracker";
import { Gun } from "../components/game/Gun";

import { BloodEffect } from "../components/game/BloodEffect";
import { BulletHole } from "../components/game/BulletHole";
import { AudioManager } from "../components/systems/AudioManager";

import { FirstPersonControls } from "../components/systems/FirstPersonControls";
import { Model } from "../models/Poc2";

// Import types
import {
  BloodEffect as BloodEffectType,
  BulletHole as BulletHoleType,
} from "../types/game";

const App = (): JSX.Element => {
  // Get game session state, UI state, and player state from Zustand store
  const { 
    gameStarted, 
    showWarning, 
    showGun,
    showCrosshair,
    showMapTracker,
    position: playerPosition,
    rotation: playerRotation,
    startGame, 
    hideWarning,
    updatePosition,
    updateRotation
  } = useAppStore();

  // Initialize player position at map center on component mount
  useEffect(() => {
    const mapCenterPosition = new Vector3(400, 1.5, 400);
    updatePosition(mapCenterPosition);
  }, [updatePosition]);

  // Keep local state for things that don't need global state management
  const [bulletHoles, setBulletHoles] = useState<BulletHoleType[]>([]);
  const [bloodEffects, setBloodEffects] = useState<BloodEffectType[]>([]);

  // These handlers now update the Zustand store
  const handlePositionUpdate = (position: Vector3): void => {
    updatePosition(position);
  };

  const handleRotationUpdate = (rotation: number): void => {
    updateRotation(rotation);
  };

  const handleWarningAccept = (): void => {
    hideWarning();
    startGame();
  };

  // Handle shooting hits
  const handleShoot = (
    hit: THREE.Intersection,
    cameraPosition: Vector3
  ): void => {
    const hitObject = hit.object;
    const hitPoint = hit.point;
    const hitNormal = hit.face?.normal;

    // Check if enemy was hit
    if (hitObject.userData?.isEntity) {
      console.log("Enemy hit!");

      // Add blood effect
      const bloodId = Date.now() + Math.random();
      setBloodEffects((prev: BloodEffectType[]) => [
        ...prev,
        {
          id: bloodId,
          position: hitPoint.clone(),
        },
      ]);

      // Damage the enemy
      if (hitObject.userData.takeDamage) {
        hitObject.userData.takeDamage(100); // One shot kill
      }
    } else if (hitNormal) {
      // Add bullet hole for wall hits
      console.log("Wall hit at:", hitPoint);
      const holeId = Date.now() + Math.random();
      const offsetPosition = hitPoint
        .clone()
        .add(hitNormal.clone().multiplyScalar(0.01));
      setBulletHoles((prev: BulletHoleType[]) => [
        ...prev,
        {
          id: holeId,
          position: offsetPosition,
          normal: hitNormal.clone(),
          cameraPosition: cameraPosition.clone(),
        },
      ]);
    }
  };

  // Remove blood effect
  const removeBloodEffect = (id: number): void => {
    setBloodEffects((prev: BloodEffectType[]) =>
      prev.filter((effect: BloodEffectType) => effect.id !== id)
    );
  };

  // Remove bullet hole
  const removeBulletHole = (id: number): void => {
    setBulletHoles((prev: BulletHoleType[]) =>
      prev.filter((hole: BulletHoleType) => hole.id !== id)
    );
  };

  return (
    <div style={{ width: "100vw", height: "100vh", position: "relative" }}>
      {/* Warning Dialog */}
      {showWarning && <WarningDialog onAccept={handleWarningAccept} />}

      {/* Silent audio manager - no UI */}
      <AudioManager />

      {/* Instructions */}
      {/* {gameStarted && <Instructions />} */}

      {/* Crosshair */}
      {gameStarted && showCrosshair && <Crosshair />}

      {/* Map Tracker - Updated with new coordinate system */}
      {gameStarted && showMapTracker && (
        <MapTracker
          playerPosition={playerPosition}
          playerRotation={playerRotation}
          mapScale={30} // Adjust this based on your game world scale
          size={250} // Size of the tracker in pixels
        />
      )}

      <Canvas
        camera={{
          fov: 75,
          position: [400, 1.5, 400], // Start at map center coordinates
          rotation: [0, 0, 0],
          near: 0.1,
          far: 1000,
        }}
        onCreated={({ camera }) => {
          camera.rotation.set(0, 0, 0);
          camera.lookAt(400, 1.5, 399); // Look forward from the starting position
        }}
      >
        {/* Enhanced atmospheric lighting for better texture visibility at map center */}
        <ambientLight intensity={0.3} color="#fff8dc" />
        <directionalLight
          position={[420, 20, 420]} // Positioned above and offset from map center
          intensity={0.8}
          color="#fff8dc"
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={100}
          shadow-camera-left={-50}
          shadow-camera-right={50}
          shadow-camera-top={50}
          shadow-camera-bottom={-50}
        />
        <directionalLight
          position={[380, 15, 380]} // Secondary light from opposite direction
          intensity={0.4}
          color="#f4e4bc"
        />
        <pointLight
          position={[400, 10, 400]} // Central point light at map center
          intensity={0.5}
          color="#fff8dc"
          distance={100}
        />

        {/* Pointer lock controls for first person view */}
        <PointerLockControls />

        {/* First person movement controller with rotation tracking */}
        <FirstPersonControls 
          onPositionUpdate={handlePositionUpdate}
          onRotationUpdate={handleRotationUpdate}
        />

        {/* The backrooms model */}
        <Model />

        {/* The gun model */}
        {gameStarted && showGun && <Gun isVisible={showGun} onShoot={handleShoot} />}

        {/* Blood effects */}
        {bloodEffects.map((effect: BloodEffectType) => (
          <BloodEffect
            key={effect.id}
            position={effect.position}
            onComplete={() => removeBloodEffect(effect.id)}
          />
        ))}

        {/* Bullet holes */}
        {bulletHoles.map((hole: BulletHoleType) => (
          <BulletHole
            key={hole.id}
            position={hole.position}
            normal={hole.normal}
            cameraPosition={hole.cameraPosition}
            onComplete={() => removeBulletHole(hole.id)}
          />
        ))}
      </Canvas>
    </div>
  );
};

export default App;