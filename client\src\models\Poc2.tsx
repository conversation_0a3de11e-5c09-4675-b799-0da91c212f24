/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.5.3 poc2.glb --draw --types 
*/

import * as THREE from 'three'
import { useGLTF } from '@react-three/drei'
import { GLTF } from 'three-stdlib'

type GLTFResult = GLTF & {
  nodes: {
    DoorFrame: THREE.Mesh
    Door: THREE.Mesh
    Handle_Back: THREE.Mesh
    Handle_Front: THREE.Mesh
    DoorFrame001: THREE.Mesh
    Door001: THREE.Mesh
    Handle_Back001: THREE.Mesh
    Handle_Front001: THREE.Mesh
    wall_pillar_thing_Material001_0001: THREE.Mesh
    wall_pillar_thing_Material001_0002: THREE.Mesh
    wall_pillar_thing_Material001_0003: THREE.Mesh
    wall_pillar_thing_Material001_0004: THREE.Mesh
    wall_pillar_thing_Material001_0005: THREE.Mesh
    wall_pillar_thing_Material001_0006: THREE.Mesh
    wall_pillar_thing_Material001_0007: THREE.Mesh
    wall_pillar_Material001_0001: THREE.Mesh
    wall_pillar_Material001_0002: THREE.Mesh
    wall_pillar_Material001_0003: THREE.Mesh
    wall_pillar_Material001_0004: THREE.Mesh
    wall_pillar_Material001_0005: THREE.Mesh
    wall_pillar_Material001_0006: THREE.Mesh
    wall_pillar_Material001_0007: THREE.Mesh
    wall_3_Material001_0001: THREE.Mesh
    wall_3_Material001_0002: THREE.Mesh
    wall_3_Material001_0003: THREE.Mesh
    wall_pillar_Material001_0008: THREE.Mesh
    wall_pillar_Material001_0009: THREE.Mesh
    wall_pillar_Material001_0010: THREE.Mesh
    wall_pillar_Material001_0011: THREE.Mesh
    wall_pillar_Material001_0012: THREE.Mesh
    wall_pillar_Material001_0013: THREE.Mesh
    wall_pillar_Material001_0014: THREE.Mesh
    wall_pillar_Material001_0015: THREE.Mesh
    wall_pillar_Material001_0016: THREE.Mesh
    wall_pillar_Material001_0017: THREE.Mesh
    wall_pillar_Material001_0018: THREE.Mesh
    wall_pillar_Material001_0019: THREE.Mesh
    wall_pillar_Material001_0020: THREE.Mesh
    wall_pillar_Material001_0021: THREE.Mesh
    wall_pillar_Material001_0022: THREE.Mesh
    wall_pillar_Material001_0023: THREE.Mesh
    wall_pillar_thing_Material001_0008: THREE.Mesh
    wall_pillar_thing_Material001_0009: THREE.Mesh
    wall_pillar_thing_Material001_0010: THREE.Mesh
    wall_pillar_thing_Material001_0011: THREE.Mesh
    wall_3_Material001_0004: THREE.Mesh
    wall_3_Material001_0005: THREE.Mesh
    wall_pillar_thing_Material001_0012: THREE.Mesh
    wall_pillar_thing_Material001_0013: THREE.Mesh
    wall_pillar_thing_Material001_0014: THREE.Mesh
    wall_pillar_thing_Material001_0015: THREE.Mesh
    wall_pillar_thing_Material001_0016: THREE.Mesh
    wall_pillar_thing_Material001_0017: THREE.Mesh
    wall_pillar_thing_Material001_0018: THREE.Mesh
    wall_pillar_thing_Material001_0019: THREE.Mesh
    wall_pillar_thing_Material001_0020: THREE.Mesh
    wall_pillar_thing_Material001_0021: THREE.Mesh
    wall_pillar_thing_Material001_0023: THREE.Mesh
    wall_pillar_Material001_0024: THREE.Mesh
    wall_pillar_Material001_0025: THREE.Mesh
    wall_pillar_Material001_0026: THREE.Mesh
    wall_pillar_Material001_0027: THREE.Mesh
    wall_pillar_Material001_0028: THREE.Mesh
    wall_pillar_Material001_0029: THREE.Mesh
    wall_pillar_Material001_0030: THREE.Mesh
    wall_pillar_thing_Material001_0022: THREE.Mesh
    wall_pillar_Material001_0031: THREE.Mesh
    wall_pillar_Material001_0032: THREE.Mesh
    wall_pillar_Material001_0033: THREE.Mesh
    wall_pillar_Material001_0034: THREE.Mesh
    wall_pillar_Material001_0035: THREE.Mesh
    wall_pillar_Material001_0036: THREE.Mesh
    wall_pillar_thing_Material001_0024: THREE.Mesh
    wall_pillar_thing_Material001_0025: THREE.Mesh
    wall_pillar_thing_Material001_0026: THREE.Mesh
    Plane: THREE.Mesh
    Cube: THREE.Mesh
    Cube001: THREE.Mesh
    Cube003: THREE.Mesh
    Cube004: THREE.Mesh
    Cube005: THREE.Mesh
    Cube006: THREE.Mesh
    Cube007: THREE.Mesh
    Cube008: THREE.Mesh
    Column: THREE.Mesh
    Column_box_bottom: THREE.Mesh
    Column_box_top: THREE.Mesh
    Cube009: THREE.Mesh
    Cube010: THREE.Mesh
    Cube011: THREE.Mesh
    Cube012: THREE.Mesh
    Cube013: THREE.Mesh
    Cube014: THREE.Mesh
    Cube015: THREE.Mesh
    Cube016: THREE.Mesh
    Cube017: THREE.Mesh
    Cube018: THREE.Mesh
    Cube019: THREE.Mesh
    Cube020: THREE.Mesh
    Cube021: THREE.Mesh
    Cube022: THREE.Mesh
    Cube023: THREE.Mesh
    Cube024: THREE.Mesh
    Cube025: THREE.Mesh
    Cube026: THREE.Mesh
    Cube027: THREE.Mesh
    Cube028: THREE.Mesh
    Cube029: THREE.Mesh
    Cube030: THREE.Mesh
    Cube031: THREE.Mesh
    Cube032: THREE.Mesh
    Cube033: THREE.Mesh
    Cube034: THREE.Mesh
    Cube035: THREE.Mesh
    Cube036: THREE.Mesh
    Cube037: THREE.Mesh
    Cube038: THREE.Mesh
    Cube039: THREE.Mesh
    Cube040: THREE.Mesh
    Cube041: THREE.Mesh
    Cube042: THREE.Mesh
    Cube043: THREE.Mesh
    Cube044: THREE.Mesh
    Cube045: THREE.Mesh
    Cube046: THREE.Mesh
    Cube047: THREE.Mesh
    Cube048: THREE.Mesh
    Cube049: THREE.Mesh
    Cube050: THREE.Mesh
    Cube051: THREE.Mesh
    Cube052: THREE.Mesh
    Cube053: THREE.Mesh
    Cube054: THREE.Mesh
    Cube055: THREE.Mesh
    Cube056: THREE.Mesh
    Cube057: THREE.Mesh
    Cube058: THREE.Mesh
    Cube059: THREE.Mesh
    Cube060: THREE.Mesh
    Column001: THREE.Mesh
    Column_box_bottom001: THREE.Mesh
    Column_box_top001: THREE.Mesh
    Cube061: THREE.Mesh
    Cube062: THREE.Mesh
    Cube063: THREE.Mesh
    Cube064: THREE.Mesh
    Cube065: THREE.Mesh
    Cube067: THREE.Mesh
    Cube068: THREE.Mesh
    Cube070: THREE.Mesh
    Cube071: THREE.Mesh
    Cube072: THREE.Mesh
    Cube073: THREE.Mesh
    Cube074: THREE.Mesh
    Cube075: THREE.Mesh
    Cube076: THREE.Mesh
    Cube077: THREE.Mesh
    Cube078: THREE.Mesh
    Plane001: THREE.Mesh
    Cube069: THREE.Mesh
    Cube079: THREE.Mesh
    Cube080: THREE.Mesh
    Cube081: THREE.Mesh
    Cube082: THREE.Mesh
    Cube066: THREE.Mesh
    Cube083: THREE.Mesh
    Cube084: THREE.Mesh
    Cube085: THREE.Mesh
    Cube086: THREE.Mesh
    Cube087: THREE.Mesh
    Cube088: THREE.Mesh
    Cube089: THREE.Mesh
    Cube090: THREE.Mesh
    Cube091: THREE.Mesh
    Cube092: THREE.Mesh
    Cube093: THREE.Mesh
    Cube094: THREE.Mesh
    Cube095: THREE.Mesh
    Cube096: THREE.Mesh
    Cube097: THREE.Mesh
    Cube098: THREE.Mesh
    Cube099: THREE.Mesh
    Cube100: THREE.Mesh
    Cube101: THREE.Mesh
    Cube102: THREE.Mesh
    Cube103: THREE.Mesh
    Cube104: THREE.Mesh
    Cube105: THREE.Mesh
    Cube106: THREE.Mesh
    Cube107: THREE.Mesh
    Cube108: THREE.Mesh
    Cube109: THREE.Mesh
    Plane002: THREE.Mesh
    Cube110: THREE.Mesh
    Cube111: THREE.Mesh
    Cube112: THREE.Mesh
    Cube113: THREE.Mesh
    Baseboard: THREE.Mesh
    Baseboard001: THREE.Mesh
    Cube002: THREE.Mesh
    Cube114: THREE.Mesh
    Cube115: THREE.Mesh
    Cube116: THREE.Mesh
    Cube117: THREE.Mesh
    Cube118: THREE.Mesh
    Cube119: THREE.Mesh
    Cube120: THREE.Mesh
    Cube121: THREE.Mesh
    Cube122: THREE.Mesh
    Cube123: THREE.Mesh
    Cube124: THREE.Mesh
    Cube126: THREE.Mesh
    Cube127: THREE.Mesh
    Cube128: THREE.Mesh
    Cube129: THREE.Mesh
    Cube130: THREE.Mesh
    Cube131: THREE.Mesh
    Cube132: THREE.Mesh
    Cube133: THREE.Mesh
    Cube134: THREE.Mesh
    Cube125: THREE.Mesh
    Cube135: THREE.Mesh
    Cube136: THREE.Mesh
    Cube137: THREE.Mesh
    Cube138: THREE.Mesh
    Cube139: THREE.Mesh
    Cube140: THREE.Mesh
    Cube141: THREE.Mesh
    Cube142: THREE.Mesh
    Cube143: THREE.Mesh
    Cube144: THREE.Mesh
    Cube145: THREE.Mesh
    Cube146: THREE.Mesh
    Cube147: THREE.Mesh
    Cube148: THREE.Mesh
    Cube149: THREE.Mesh
    Cube150: THREE.Mesh
    Cube151: THREE.Mesh
    Cube152: THREE.Mesh
    Cube153: THREE.Mesh
    Cube154: THREE.Mesh
    Cube155: THREE.Mesh
    Cube156: THREE.Mesh
    Cube157: THREE.Mesh
    Cube158: THREE.Mesh
    Cube159: THREE.Mesh
    Cube160: THREE.Mesh
    Cube161: THREE.Mesh
    Cube162: THREE.Mesh
    Cube164: THREE.Mesh
    Cube163: THREE.Mesh
    Cube165: THREE.Mesh
    Cube166: THREE.Mesh
    Cube167: THREE.Mesh
    Cube168: THREE.Mesh
    Cube169: THREE.Mesh
    Cube170: THREE.Mesh
    Cube171: THREE.Mesh
    Cube172: THREE.Mesh
    Cube173: THREE.Mesh
    Cube174: THREE.Mesh
    Cube175: THREE.Mesh
    Cube176: THREE.Mesh
    Cube177: THREE.Mesh
    Cube178: THREE.Mesh
    Cube179: THREE.Mesh
    Cube180: THREE.Mesh
    Cube181: THREE.Mesh
    Cube182: THREE.Mesh
    Cube183: THREE.Mesh
    Cube184: THREE.Mesh
    Cube185: THREE.Mesh
    Cube186: THREE.Mesh
    Cube187: THREE.Mesh
    Cube188: THREE.Mesh
    Cube189: THREE.Mesh
    Cube190: THREE.Mesh
    Cube191: THREE.Mesh
    Cube192: THREE.Mesh
    Cube193: THREE.Mesh
    Cube194: THREE.Mesh
    Cube195: THREE.Mesh
    Cube196: THREE.Mesh
    Cube197: THREE.Mesh
    Cube198: THREE.Mesh
    Cube199: THREE.Mesh
    Cube200: THREE.Mesh
    Cube201: THREE.Mesh
    Cube202: THREE.Mesh
    Cube203: THREE.Mesh
    Cube204: THREE.Mesh
    Cube205: THREE.Mesh
    Cube206: THREE.Mesh
    Cube207: THREE.Mesh
    Cube208: THREE.Mesh
    Cube209: THREE.Mesh
    Cube210: THREE.Mesh
    Cube211: THREE.Mesh
    Cube212: THREE.Mesh
    Cube213: THREE.Mesh
    Cube214: THREE.Mesh
    Cube215: THREE.Mesh
    Cube216: THREE.Mesh
    Cube217: THREE.Mesh
    Cube218: THREE.Mesh
    Cube219: THREE.Mesh
    Cube220: THREE.Mesh
    Cube221: THREE.Mesh
    Cube222: THREE.Mesh
    Cube223: THREE.Mesh
    Cube224: THREE.Mesh
    Cube225: THREE.Mesh
    Cube226: THREE.Mesh
    Cube227: THREE.Mesh
    Cube228: THREE.Mesh
    Cube229: THREE.Mesh
    Cube230: THREE.Mesh
    Cube231: THREE.Mesh
    Cube232: THREE.Mesh
    Cube233: THREE.Mesh
    Cube234: THREE.Mesh
    Cube235: THREE.Mesh
    Cube236: THREE.Mesh
    Cube237: THREE.Mesh
    Cube238: THREE.Mesh
    Cube239: THREE.Mesh
    Cube240: THREE.Mesh
    Cube241: THREE.Mesh
    Cube242: THREE.Mesh
    Cube243: THREE.Mesh
    Cube244: THREE.Mesh
    Cube245: THREE.Mesh
    Cube246: THREE.Mesh
    Cube247: THREE.Mesh
    Cube248: THREE.Mesh
    Cube249: THREE.Mesh
    Cube250: THREE.Mesh
    Cube251: THREE.Mesh
    Cube252: THREE.Mesh
    Cube253: THREE.Mesh
    Cube254: THREE.Mesh
    Cube255: THREE.Mesh
    Cube256: THREE.Mesh
    Cube257: THREE.Mesh
    Cube258: THREE.Mesh
    Cube259: THREE.Mesh
    Cube260: THREE.Mesh
    Cube261: THREE.Mesh
    Cube262: THREE.Mesh
    Cube263: THREE.Mesh
    Cube264: THREE.Mesh
    Cube265: THREE.Mesh
    Cube266: THREE.Mesh
    Cube267: THREE.Mesh
    Cube268: THREE.Mesh
    Cube269: THREE.Mesh
    Cube270: THREE.Mesh
    Cube271: THREE.Mesh
    Cube272: THREE.Mesh
    Cube273: THREE.Mesh
    Cube274: THREE.Mesh
    Cube275: THREE.Mesh
    Column002: THREE.Mesh
    Column_box_top002: THREE.Mesh
    Column003: THREE.Mesh
    Column_box_top003: THREE.Mesh
    Column004: THREE.Mesh
    Column_box_top004: THREE.Mesh
    Column005: THREE.Mesh
    Column_box_top005: THREE.Mesh
    Column006: THREE.Mesh
    Column_box_top006: THREE.Mesh
    Column007: THREE.Mesh
    Column_box_top007: THREE.Mesh
    Column008: THREE.Mesh
    Column_box_top008: THREE.Mesh
    Column009: THREE.Mesh
    Column_box_top009: THREE.Mesh
    Column010: THREE.Mesh
    Column_box_top010: THREE.Mesh
    Column011: THREE.Mesh
    Column_box_top011: THREE.Mesh
    Column012: THREE.Mesh
    Column_box_top012: THREE.Mesh
    Column013: THREE.Mesh
    Column_box_top013: THREE.Mesh
    Column014: THREE.Mesh
    Column_box_top014: THREE.Mesh
    Column015: THREE.Mesh
    Column_box_top015: THREE.Mesh
    Column016: THREE.Mesh
    Column_box_top016: THREE.Mesh
    Column017: THREE.Mesh
    Column_box_top017: THREE.Mesh
    Column018: THREE.Mesh
    Column_box_top018: THREE.Mesh
    Column019: THREE.Mesh
    Column_box_top019: THREE.Mesh
    Column020: THREE.Mesh
    Column_box_top020: THREE.Mesh
    Column021: THREE.Mesh
    Column_box_top021: THREE.Mesh
    Column022: THREE.Mesh
    Column_box_top022: THREE.Mesh
    Column023: THREE.Mesh
    Column_box_top023: THREE.Mesh
    Column024: THREE.Mesh
    Column_box_top024: THREE.Mesh
    Column025: THREE.Mesh
    Column_box_top025: THREE.Mesh
    Column026: THREE.Mesh
    Column_box_top026: THREE.Mesh
    Column027: THREE.Mesh
    Column_box_top027: THREE.Mesh
    Column028: THREE.Mesh
    Column029: THREE.Mesh
    Column_box_top028: THREE.Mesh
    Column030: THREE.Mesh
    Column_box_top029: THREE.Mesh
    Column031: THREE.Mesh
    Column_box_top030: THREE.Mesh
    Column032: THREE.Mesh
    Column_box_top031: THREE.Mesh
    Column033: THREE.Mesh
    Column_box_top032: THREE.Mesh
    Column034: THREE.Mesh
    Column_box_top033: THREE.Mesh
    Column035: THREE.Mesh
    Column_box_top034: THREE.Mesh
    Column036: THREE.Mesh
    Column_box_top035: THREE.Mesh
    Column037: THREE.Mesh
    Column_box_top036: THREE.Mesh
    Column038: THREE.Mesh
    Column_box_top037: THREE.Mesh
    Column039: THREE.Mesh
    Column_box_top038: THREE.Mesh
    Column040: THREE.Mesh
    Column_box_top039: THREE.Mesh
    Column041: THREE.Mesh
    Column_box_top040: THREE.Mesh
    Column042: THREE.Mesh
    Column_box_top041: THREE.Mesh
    Column043: THREE.Mesh
    Column044: THREE.Mesh
    Column_box_top042: THREE.Mesh
    Column045: THREE.Mesh
    Column_box_top043: THREE.Mesh
    Column046: THREE.Mesh
    Column_box_top044: THREE.Mesh
    Column047: THREE.Mesh
    Column_box_top045: THREE.Mesh
    Column048: THREE.Mesh
    Column049: THREE.Mesh
    Column_box_top046: THREE.Mesh
    Column050: THREE.Mesh
    Column_box_top047: THREE.Mesh
    Column051: THREE.Mesh
    Column_box_top048: THREE.Mesh
    Column052: THREE.Mesh
    Column_box_top049: THREE.Mesh
    Column053: THREE.Mesh
    Column_box_top050: THREE.Mesh
    Column054: THREE.Mesh
    Column_box_top051: THREE.Mesh
    Column055: THREE.Mesh
    Column_box_top052: THREE.Mesh
    Column056: THREE.Mesh
    Column_box_top053: THREE.Mesh
    Column057: THREE.Mesh
    Column_box_top054: THREE.Mesh
    Column058: THREE.Mesh
    Column_box_top055: THREE.Mesh
    Column059: THREE.Mesh
    Column_box_top056: THREE.Mesh
    Column060: THREE.Mesh
    Column_box_top057: THREE.Mesh
    Column061: THREE.Mesh
    Column_box_top058: THREE.Mesh
    Column062: THREE.Mesh
    Column_box_top059: THREE.Mesh
    Column063: THREE.Mesh
    Column064: THREE.Mesh
    Column_box_top060: THREE.Mesh
    Column065: THREE.Mesh
    Column_box_top061: THREE.Mesh
    Column066: THREE.Mesh
    Column_box_top062: THREE.Mesh
    Column067: THREE.Mesh
    Column_box_top063: THREE.Mesh
    Column068: THREE.Mesh
    Column_box_top064: THREE.Mesh
    Column069: THREE.Mesh
    Column_box_top065: THREE.Mesh
    Column070: THREE.Mesh
    Column_box_top066: THREE.Mesh
    Column071: THREE.Mesh
    Column_box_top067: THREE.Mesh
    Column072: THREE.Mesh
    Column_box_top068: THREE.Mesh
    Column073: THREE.Mesh
    Column074: THREE.Mesh
    Column_box_top069: THREE.Mesh
    Column075: THREE.Mesh
    Column_box_top070: THREE.Mesh
    Column076: THREE.Mesh
    Column_box_top071: THREE.Mesh
    Column077: THREE.Mesh
    Column_box_top072: THREE.Mesh
    Column078: THREE.Mesh
    Column079: THREE.Mesh
    Column_box_top073: THREE.Mesh
    Column080: THREE.Mesh
    Column_box_top074: THREE.Mesh
    Column081: THREE.Mesh
    Column_box_top075: THREE.Mesh
    Column082: THREE.Mesh
    Column_box_top076: THREE.Mesh
    Column083: THREE.Mesh
    Column_box_top077: THREE.Mesh
    Column084: THREE.Mesh
    Column_box_top078: THREE.Mesh
    Column085: THREE.Mesh
    Column_box_top079: THREE.Mesh
    Cube276: THREE.Mesh
    Cube277: THREE.Mesh
    Cube278: THREE.Mesh
    Cube279: THREE.Mesh
    Cube280: THREE.Mesh
    Cube281: THREE.Mesh
    Cube282: THREE.Mesh
    Cube283: THREE.Mesh
    Cube284: THREE.Mesh
    Cube285: THREE.Mesh
    Cube286: THREE.Mesh
    Cube287: THREE.Mesh
    Column086: THREE.Mesh
    Column087: THREE.Mesh
    Column088: THREE.Mesh
    Column089: THREE.Mesh
    Column090: THREE.Mesh
    Column091: THREE.Mesh
    Column092: THREE.Mesh
    Column093: THREE.Mesh
    Column094: THREE.Mesh
    Cube288: THREE.Mesh
    Cube289: THREE.Mesh
    Cube290: THREE.Mesh
    Cube291: THREE.Mesh
    Cube292: THREE.Mesh
    Cube293: THREE.Mesh
    Cube294: THREE.Mesh
    Cube295: THREE.Mesh
    Cube296: THREE.Mesh
    Cube297: THREE.Mesh
    Cube298: THREE.Mesh
    Cube299: THREE.Mesh
    Cube300: THREE.Mesh
    Cube301: THREE.Mesh
    Cube302: THREE.Mesh
    Cube303: THREE.Mesh
    Cube304: THREE.Mesh
    Cube305: THREE.Mesh
    Cube306: THREE.Mesh
    Cube307: THREE.Mesh
    Cube308: THREE.Mesh
    Cube309: THREE.Mesh
    Cube310: THREE.Mesh
    Cube311: THREE.Mesh
    Cube312: THREE.Mesh
    Cube314: THREE.Mesh
    Cube315: THREE.Mesh
    Column095: THREE.Mesh
    Column096: THREE.Mesh
    Column097: THREE.Mesh
    Column098: THREE.Mesh
    Column099: THREE.Mesh
    Column100: THREE.Mesh
    Column101: THREE.Mesh
    Cube313: THREE.Mesh
    Column102: THREE.Mesh
    Cube316: THREE.Mesh
    Cube317: THREE.Mesh
    Cube318: THREE.Mesh
    Cube319: THREE.Mesh
    Cube320: THREE.Mesh
    Column103: THREE.Mesh
    Column104: THREE.Mesh
    Cube321: THREE.Mesh
    Cube322: THREE.Mesh
    Cube323: THREE.Mesh
    Cube324: THREE.Mesh
    Cube325: THREE.Mesh
  }
  materials: {
    Door_material: THREE.MeshStandardMaterial
    Handle_material: THREE.MeshStandardMaterial
    ['Material.020']: THREE.MeshStandardMaterial
    ['Material.021']: THREE.MeshStandardMaterial
    ['Material.022']: THREE.MeshStandardMaterial
    ['Material.023']: THREE.MeshStandardMaterial
    ['Material.024']: THREE.MeshStandardMaterial
    ['Material.025']: THREE.MeshStandardMaterial
    ['Material.026']: THREE.MeshStandardMaterial
    ['Material.029']: THREE.MeshStandardMaterial
    ['Material.030']: THREE.MeshStandardMaterial
    ['Material.031']: THREE.MeshStandardMaterial
    ['Material.032']: THREE.MeshStandardMaterial
    ['Material.033']: THREE.MeshStandardMaterial
    ['Material.034']: THREE.MeshStandardMaterial
    ['Material.035']: THREE.MeshStandardMaterial
    ['Material.044']: THREE.MeshStandardMaterial
    ['Material.045']: THREE.MeshStandardMaterial
    ['Material.046']: THREE.MeshStandardMaterial
    ['Material.049']: THREE.MeshStandardMaterial
    ['Material.050']: THREE.MeshStandardMaterial
    ['Material.051']: THREE.MeshStandardMaterial
    ['Material.052']: THREE.MeshStandardMaterial
    ['Material.053']: THREE.MeshStandardMaterial
    ['Material.054']: THREE.MeshStandardMaterial
    ['Material.055']: THREE.MeshStandardMaterial
    ['Material.056']: THREE.MeshStandardMaterial
    ['Material.057']: THREE.MeshStandardMaterial
    ['Material.058']: THREE.MeshStandardMaterial
    ['Material.059']: THREE.MeshStandardMaterial
    ['Material.060']: THREE.MeshStandardMaterial
    ['Material.061']: THREE.MeshStandardMaterial
    ['Material.062']: THREE.MeshStandardMaterial
    ['Material.063']: THREE.MeshStandardMaterial
    ['Material.071']: THREE.MeshStandardMaterial
    ['Material.082']: THREE.MeshStandardMaterial
    ['Material.083']: THREE.MeshStandardMaterial
    ['Material.084']: THREE.MeshStandardMaterial
    ['Material.085']: THREE.MeshStandardMaterial
    ['Material.086']: THREE.MeshStandardMaterial
    ['Material.090']: THREE.MeshStandardMaterial
    ['Material.134']: THREE.MeshStandardMaterial
    ['Material.135']: THREE.MeshStandardMaterial
    ['Material.136']: THREE.MeshStandardMaterial
    ['Material.137']: THREE.MeshStandardMaterial
    ['Material.138']: THREE.MeshStandardMaterial
    ['Material.139']: THREE.MeshStandardMaterial
    ['Material.140']: THREE.MeshStandardMaterial
    ['Material.141']: THREE.MeshStandardMaterial
    ['Material.142']: THREE.MeshStandardMaterial
    ['Material.143']: THREE.MeshStandardMaterial
    ['Material.145']: THREE.MeshStandardMaterial
    ['Material.149']: THREE.MeshStandardMaterial
    ['Material.150']: THREE.MeshStandardMaterial
    ['Material.151']: THREE.MeshStandardMaterial
    ['Material.152']: THREE.MeshStandardMaterial
    ['Material.153']: THREE.MeshStandardMaterial
    ['Material.154']: THREE.MeshStandardMaterial
    ['Material.155']: THREE.MeshStandardMaterial
    ['Material.156']: THREE.MeshStandardMaterial
    ['Material.157']: THREE.MeshStandardMaterial
    ['Material.158']: THREE.MeshStandardMaterial
    ['Material.159']: THREE.MeshStandardMaterial
    ['Material.160']: THREE.MeshStandardMaterial
    ['Material.165']: THREE.MeshStandardMaterial
    ['Material.166']: THREE.MeshStandardMaterial
    ['Material.168']: THREE.MeshStandardMaterial
    ['Material.169']: THREE.MeshStandardMaterial
    ['Material.170']: THREE.MeshStandardMaterial
    Column_material: THREE.MeshStandardMaterial
    Column_rect: THREE.MeshStandardMaterial
    ['Material.001']: THREE.MeshStandardMaterial
    ['Column_material.001']: THREE.MeshStandardMaterial
    ['Column_rect.001']: THREE.MeshStandardMaterial
    ['Material.002']: THREE.MeshStandardMaterial
    ['Material.003']: THREE.MeshStandardMaterial
    ['Material.004']: THREE.MeshStandardMaterial
    ['Material.005']: THREE.MeshStandardMaterial
    Baseboard_material: THREE.MeshStandardMaterial
    ['Material.008']: THREE.MeshStandardMaterial
    ['Material.007']: THREE.MeshStandardMaterial
    ['Material.009']: THREE.MeshStandardMaterial
    ['Material.010']: THREE.MeshStandardMaterial
    ['Material.011']: THREE.MeshStandardMaterial
    ['Material.012']: THREE.MeshStandardMaterial
    ['Material.013']: THREE.MeshStandardMaterial
    ['Material.014']: THREE.MeshStandardMaterial
    ['Material.015']: THREE.MeshStandardMaterial
    ['Material.016']: THREE.MeshStandardMaterial
    ['Material.017']: THREE.MeshStandardMaterial
    ['Material.018']: THREE.MeshStandardMaterial
    ['Material.027']: THREE.MeshStandardMaterial
    ['Material.028']: THREE.MeshStandardMaterial
    ['Material.036']: THREE.MeshStandardMaterial
    ['Material.037']: THREE.MeshStandardMaterial
    ['Material.038']: THREE.MeshStandardMaterial
    ['Material.039']: THREE.MeshStandardMaterial
    ['Material.040']: THREE.MeshStandardMaterial
    ['Material.041']: THREE.MeshStandardMaterial
    ['Material.042']: THREE.MeshStandardMaterial
    ['Material.043']: THREE.MeshStandardMaterial
    ['Material.047']: THREE.MeshStandardMaterial
    ['Material.065']: THREE.MeshStandardMaterial
    ['Material.066']: THREE.MeshStandardMaterial
    ['Material.068']: THREE.MeshStandardMaterial
    ['Material.069']: THREE.MeshStandardMaterial
    ['Material.070']: THREE.MeshStandardMaterial
    ['Material.072']: THREE.MeshStandardMaterial
    ['Material.073']: THREE.MeshStandardMaterial
    ['Material.074']: THREE.MeshStandardMaterial
    ['Material.075']: THREE.MeshStandardMaterial
    ['Material.076']: THREE.MeshStandardMaterial
    ['Material.077']: THREE.MeshStandardMaterial
    ['Material.078']: THREE.MeshStandardMaterial
    ['Material.079']: THREE.MeshStandardMaterial
    ['Material.080']: THREE.MeshStandardMaterial
    ['Material.081']: THREE.MeshStandardMaterial
    ['Material.087']: THREE.MeshStandardMaterial
    ['Material.088']: THREE.MeshStandardMaterial
    ['Material.089']: THREE.MeshStandardMaterial
    ['Material.091']: THREE.MeshStandardMaterial
    ['Material.092']: THREE.MeshStandardMaterial
    ['Material.093']: THREE.MeshStandardMaterial
    ['Material.094']: THREE.MeshStandardMaterial
    ['Material.095']: THREE.MeshStandardMaterial
    ['Material.096']: THREE.MeshStandardMaterial
    ['Material.098']: THREE.MeshStandardMaterial
    ['Material.099']: THREE.MeshStandardMaterial
    ['Material.100']: THREE.MeshStandardMaterial
    ['Material.101']: THREE.MeshStandardMaterial
    ['Material.102']: THREE.MeshStandardMaterial
    ['Material.103']: THREE.MeshStandardMaterial
    ['Material.104']: THREE.MeshStandardMaterial
    ['Material.105']: THREE.MeshStandardMaterial
    ['Material.106']: THREE.MeshStandardMaterial
    ['Material.107']: THREE.MeshStandardMaterial
    ['Material.108']: THREE.MeshStandardMaterial
    ['Material.109']: THREE.MeshStandardMaterial
    ['Material.110']: THREE.MeshStandardMaterial
    ['Material.111']: THREE.MeshStandardMaterial
    ['Material.112']: THREE.MeshStandardMaterial
    ['Material.113']: THREE.MeshStandardMaterial
    ['Material.114']: THREE.MeshStandardMaterial
    ['Material.115']: THREE.MeshStandardMaterial
    ['Material.116']: THREE.MeshStandardMaterial
    ['Material.117']: THREE.MeshStandardMaterial
    ['Material.118']: THREE.MeshStandardMaterial
    ['Material.119']: THREE.MeshStandardMaterial
    ['Material.120']: THREE.MeshStandardMaterial
    ['Material.121']: THREE.MeshStandardMaterial
    ['Material.122']: THREE.MeshStandardMaterial
    ['Material.123']: THREE.MeshStandardMaterial
    ['Material.124']: THREE.MeshStandardMaterial
    ['Material.125']: THREE.MeshStandardMaterial
    ['Material.126']: THREE.MeshStandardMaterial
    ['Material.127']: THREE.MeshStandardMaterial
    ['Material.128']: THREE.MeshStandardMaterial
    ['Material.129']: THREE.MeshStandardMaterial
    ['Material.130']: THREE.MeshStandardMaterial
    ['Material.131']: THREE.MeshStandardMaterial
    ['Material.132']: THREE.MeshStandardMaterial
    ['Material.133']: THREE.MeshStandardMaterial
    ['Material.146']: THREE.MeshStandardMaterial
    ['Material.147']: THREE.MeshStandardMaterial
    ['Material.148']: THREE.MeshStandardMaterial
    ['Material.161']: THREE.MeshStandardMaterial
    ['Material.162']: THREE.MeshStandardMaterial
    ['Material.163']: THREE.MeshStandardMaterial
    ['Material.164']: THREE.MeshStandardMaterial
    ['Material.167']: THREE.MeshStandardMaterial
    ['Column_material.002']: THREE.MeshStandardMaterial
    ['Column_rect.002']: THREE.MeshStandardMaterial
    ['Column_material.003']: THREE.MeshStandardMaterial
    ['Column_rect.003']: THREE.MeshStandardMaterial
    ['Column_material.004']: THREE.MeshStandardMaterial
    ['Column_rect.004']: THREE.MeshStandardMaterial
    ['Column_material.005']: THREE.MeshStandardMaterial
    ['Column_rect.005']: THREE.MeshStandardMaterial
    ['Column_material.006']: THREE.MeshStandardMaterial
    ['Column_rect.006']: THREE.MeshStandardMaterial
    ['Column_material.007']: THREE.MeshStandardMaterial
    ['Column_rect.007']: THREE.MeshStandardMaterial
    ['Column_material.008']: THREE.MeshStandardMaterial
    ['Column_rect.008']: THREE.MeshStandardMaterial
    ['Column_material.009']: THREE.MeshStandardMaterial
    ['Column_rect.009']: THREE.MeshStandardMaterial
    ['Column_material.010']: THREE.MeshStandardMaterial
    ['Column_rect.010']: THREE.MeshStandardMaterial
    ['Column_material.011']: THREE.MeshStandardMaterial
    ['Column_rect.011']: THREE.MeshStandardMaterial
    ['Column_material.012']: THREE.MeshStandardMaterial
    ['Column_rect.012']: THREE.MeshStandardMaterial
    ['Column_material.013']: THREE.MeshStandardMaterial
    ['Column_rect.013']: THREE.MeshStandardMaterial
    ['Column_material.014']: THREE.MeshStandardMaterial
    ['Column_rect.014']: THREE.MeshStandardMaterial
    ['Column_material.015']: THREE.MeshStandardMaterial
    ['Column_rect.015']: THREE.MeshStandardMaterial
    ['Column_material.016']: THREE.MeshStandardMaterial
    ['Column_rect.016']: THREE.MeshStandardMaterial
    ['Column_material.017']: THREE.MeshStandardMaterial
    ['Column_rect.017']: THREE.MeshStandardMaterial
    ['Column_material.018']: THREE.MeshStandardMaterial
    ['Column_rect.018']: THREE.MeshStandardMaterial
    ['Column_material.019']: THREE.MeshStandardMaterial
    ['Column_rect.019']: THREE.MeshStandardMaterial
    ['Column_material.020']: THREE.MeshStandardMaterial
    ['Column_rect.020']: THREE.MeshStandardMaterial
    ['Column_material.021']: THREE.MeshStandardMaterial
    ['Column_rect.021']: THREE.MeshStandardMaterial
    ['Column_material.022']: THREE.MeshStandardMaterial
    ['Column_rect.022']: THREE.MeshStandardMaterial
    ['Column_material.023']: THREE.MeshStandardMaterial
    ['Column_rect.023']: THREE.MeshStandardMaterial
    ['Column_material.024']: THREE.MeshStandardMaterial
    ['Column_rect.024']: THREE.MeshStandardMaterial
    ['Column_material.025']: THREE.MeshStandardMaterial
    ['Column_rect.025']: THREE.MeshStandardMaterial
    ['Column_material.026']: THREE.MeshStandardMaterial
    ['Column_rect.026']: THREE.MeshStandardMaterial
    ['Column_material.027']: THREE.MeshStandardMaterial
    ['Column_rect.027']: THREE.MeshStandardMaterial
    ['Column_material.028']: THREE.MeshStandardMaterial
    ['Column_material.029']: THREE.MeshStandardMaterial
    ['Column_rect.028']: THREE.MeshStandardMaterial
    ['Column_material.030']: THREE.MeshStandardMaterial
    ['Column_rect.029']: THREE.MeshStandardMaterial
    ['Column_material.031']: THREE.MeshStandardMaterial
    ['Column_rect.030']: THREE.MeshStandardMaterial
    ['Column_material.032']: THREE.MeshStandardMaterial
    ['Column_rect.031']: THREE.MeshStandardMaterial
    ['Column_material.033']: THREE.MeshStandardMaterial
    ['Column_rect.032']: THREE.MeshStandardMaterial
    ['Column_material.034']: THREE.MeshStandardMaterial
    ['Column_rect.033']: THREE.MeshStandardMaterial
    ['Column_material.035']: THREE.MeshStandardMaterial
    ['Column_rect.034']: THREE.MeshStandardMaterial
    ['Column_material.036']: THREE.MeshStandardMaterial
    ['Column_rect.035']: THREE.MeshStandardMaterial
    ['Column_material.037']: THREE.MeshStandardMaterial
    ['Column_rect.036']: THREE.MeshStandardMaterial
    ['Column_material.038']: THREE.MeshStandardMaterial
    ['Column_rect.037']: THREE.MeshStandardMaterial
    ['Column_material.039']: THREE.MeshStandardMaterial
    ['Column_rect.038']: THREE.MeshStandardMaterial
    ['Column_material.040']: THREE.MeshStandardMaterial
    ['Column_rect.039']: THREE.MeshStandardMaterial
    ['Column_material.041']: THREE.MeshStandardMaterial
    ['Column_rect.040']: THREE.MeshStandardMaterial
    ['Column_material.042']: THREE.MeshStandardMaterial
    ['Column_rect.041']: THREE.MeshStandardMaterial
    ['Column_material.043']: THREE.MeshStandardMaterial
    ['Column_material.044']: THREE.MeshStandardMaterial
    ['Column_rect.042']: THREE.MeshStandardMaterial
    ['Column_material.045']: THREE.MeshStandardMaterial
    ['Column_rect.043']: THREE.MeshStandardMaterial
    ['Column_material.046']: THREE.MeshStandardMaterial
    ['Column_rect.044']: THREE.MeshStandardMaterial
    ['Column_material.047']: THREE.MeshStandardMaterial
    ['Column_rect.045']: THREE.MeshStandardMaterial
    ['Column_material.048']: THREE.MeshStandardMaterial
    ['Column_material.049']: THREE.MeshStandardMaterial
    ['Column_rect.046']: THREE.MeshStandardMaterial
    ['Column_material.050']: THREE.MeshStandardMaterial
    ['Column_rect.047']: THREE.MeshStandardMaterial
    ['Column_material.051']: THREE.MeshStandardMaterial
    ['Column_rect.048']: THREE.MeshStandardMaterial
    ['Column_material.052']: THREE.MeshStandardMaterial
    ['Column_rect.049']: THREE.MeshStandardMaterial
    ['Column_material.053']: THREE.MeshStandardMaterial
    ['Column_rect.050']: THREE.MeshStandardMaterial
    ['Column_material.054']: THREE.MeshStandardMaterial
    ['Column_rect.051']: THREE.MeshStandardMaterial
    ['Column_material.055']: THREE.MeshStandardMaterial
    ['Column_rect.052']: THREE.MeshStandardMaterial
    ['Column_material.056']: THREE.MeshStandardMaterial
    ['Column_rect.053']: THREE.MeshStandardMaterial
    ['Column_material.057']: THREE.MeshStandardMaterial
    ['Column_rect.054']: THREE.MeshStandardMaterial
    ['Column_material.058']: THREE.MeshStandardMaterial
    ['Column_rect.055']: THREE.MeshStandardMaterial
    ['Column_material.059']: THREE.MeshStandardMaterial
    ['Column_rect.056']: THREE.MeshStandardMaterial
    ['Column_material.060']: THREE.MeshStandardMaterial
    ['Column_rect.057']: THREE.MeshStandardMaterial
    ['Column_material.061']: THREE.MeshStandardMaterial
    ['Column_rect.058']: THREE.MeshStandardMaterial
    ['Column_material.062']: THREE.MeshStandardMaterial
    ['Column_rect.059']: THREE.MeshStandardMaterial
    ['Column_material.063']: THREE.MeshStandardMaterial
    ['Column_material.064']: THREE.MeshStandardMaterial
    ['Column_rect.060']: THREE.MeshStandardMaterial
    ['Column_material.065']: THREE.MeshStandardMaterial
    ['Column_rect.061']: THREE.MeshStandardMaterial
    ['Column_material.066']: THREE.MeshStandardMaterial
    ['Column_rect.062']: THREE.MeshStandardMaterial
    ['Column_material.067']: THREE.MeshStandardMaterial
    ['Column_rect.063']: THREE.MeshStandardMaterial
    ['Column_material.068']: THREE.MeshStandardMaterial
    ['Column_rect.064']: THREE.MeshStandardMaterial
    ['Column_material.069']: THREE.MeshStandardMaterial
    ['Column_rect.065']: THREE.MeshStandardMaterial
    ['Column_material.070']: THREE.MeshStandardMaterial
    ['Column_rect.066']: THREE.MeshStandardMaterial
    ['Column_material.071']: THREE.MeshStandardMaterial
    ['Column_rect.067']: THREE.MeshStandardMaterial
    ['Column_material.072']: THREE.MeshStandardMaterial
    ['Column_rect.068']: THREE.MeshStandardMaterial
    ['Column_material.073']: THREE.MeshStandardMaterial
    ['Column_material.074']: THREE.MeshStandardMaterial
    ['Column_rect.069']: THREE.MeshStandardMaterial
    ['Column_material.075']: THREE.MeshStandardMaterial
    ['Column_rect.070']: THREE.MeshStandardMaterial
    ['Column_material.076']: THREE.MeshStandardMaterial
    ['Column_rect.071']: THREE.MeshStandardMaterial
    ['Column_material.077']: THREE.MeshStandardMaterial
    ['Column_rect.072']: THREE.MeshStandardMaterial
    ['Column_material.078']: THREE.MeshStandardMaterial
    ['Column_material.079']: THREE.MeshStandardMaterial
    ['Column_rect.073']: THREE.MeshStandardMaterial
    ['Column_material.080']: THREE.MeshStandardMaterial
    ['Column_rect.074']: THREE.MeshStandardMaterial
    ['Column_material.081']: THREE.MeshStandardMaterial
    ['Column_rect.075']: THREE.MeshStandardMaterial
    ['Column_material.082']: THREE.MeshStandardMaterial
    ['Column_rect.076']: THREE.MeshStandardMaterial
    ['Column_material.083']: THREE.MeshStandardMaterial
    ['Column_rect.077']: THREE.MeshStandardMaterial
    ['Column_material.084']: THREE.MeshStandardMaterial
    ['Column_rect.078']: THREE.MeshStandardMaterial
    ['Column_material.085']: THREE.MeshStandardMaterial
    ['Column_rect.079']: THREE.MeshStandardMaterial
    ['Column_material.086']: THREE.MeshStandardMaterial
    ['Column_material.087']: THREE.MeshStandardMaterial
    ['Column_material.088']: THREE.MeshStandardMaterial
    ['Column_material.089']: THREE.MeshStandardMaterial
    ['Column_material.090']: THREE.MeshStandardMaterial
    ['Column_material.091']: THREE.MeshStandardMaterial
    ['Column_material.092']: THREE.MeshStandardMaterial
    ['Column_material.093']: THREE.MeshStandardMaterial
    ['Column_material.094']: THREE.MeshStandardMaterial
    ['Column_material.095']: THREE.MeshStandardMaterial
    ['Column_material.096']: THREE.MeshStandardMaterial
    ['Column_material.097']: THREE.MeshStandardMaterial
    ['Column_material.098']: THREE.MeshStandardMaterial
    ['Column_material.099']: THREE.MeshStandardMaterial
    ['Column_material.100']: THREE.MeshStandardMaterial
    ['Column_material.101']: THREE.MeshStandardMaterial
    ['Material.048']: THREE.MeshStandardMaterial
    ['Column_material.102']: THREE.MeshStandardMaterial
    ['Column_material.103']: THREE.MeshStandardMaterial
    ['Column_material.104']: THREE.MeshStandardMaterial
    Material: THREE.MeshStandardMaterial
    ['Material.064']: THREE.MeshStandardMaterial
    ['Material.067']: THREE.MeshStandardMaterial
    ['Material.097']: THREE.MeshStandardMaterial
  }
}

export function Model(props: JSX.IntrinsicElements['group']) {
  const { nodes, materials } = useGLTF('/poc2.glb') as GLTFResult
  return (
    <group {...props} dispose={null}>
      <group position={[400, 0, 400]}>
      <group position={[-73.665, 0, -107.622]}>
        <mesh geometry={nodes.DoorFrame.geometry} material={materials.Door_material} position={[-0.22, 0, 0.46]} rotation={[0, Math.PI / 2, 0]} scale={[1.757, 1.487, 1]}>
          <mesh geometry={nodes.Door.geometry} material={materials.Door_material} position={[0.418, 1.05, 0.022]}>
            <mesh geometry={nodes.Handle_Back.geometry} material={materials.Handle_material} position={[-0.764, 0, -0.005]} />
            <mesh geometry={nodes.Handle_Front.geometry} material={materials.Handle_material} position={[-0.764, 0, -0.029]} rotation={[-Math.PI, 0, 0]} />
          </mesh>
        </mesh>
      </group>
      <group position={[-90.222, 0, -87.969]}>
        <mesh geometry={nodes.DoorFrame001.geometry} material={materials.Door_material} position={[0.13, 0, -0.73]} scale={[1.503, 1.176, 1]}>
          <mesh geometry={nodes.Door001.geometry} material={materials.Door_material} position={[0.418, 1.05, 0.022]}>
            <mesh geometry={nodes.Handle_Back001.geometry} material={materials.Handle_material} position={[-0.764, 0, -0.005]} />
            <mesh geometry={nodes.Handle_Front001.geometry} material={materials.Handle_material} position={[-0.764, 0, -0.029]} rotation={[-Math.PI, 0, 0]} />
          </mesh>
        </mesh>
      </group>
      <group position={[31.249, 0, -107.456]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, 0.001]}>
            <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_thing_Material001_0001.geometry} material={materials['Material.020']} position={[-41.463, 8.453, 0]} scale={[1.585, 1.636, 2.03]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[31.249, 0, -110.941]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0002.geometry} material={materials['Material.021']} position={[-48.599, 9.477, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[31.249, 0, -115.306]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, 0.001]}>
            <group position={[-204.219, 0, -413.62]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_thing_Material001_0003.geometry} material={materials['Material.022']} position={[-41.463, 8.453, 0]} scale={[1.585, 1.636, 2.03]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[31.249, 0, -100.059]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0004.geometry} material={materials['Material.023']} position={[-41.463, 8.453, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[44.895, 0, -101.319]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0005.geometry} material={materials['Material.024']} position={[-48.47, 10.578, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[31.249, 0, -110.941]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0006.geometry} material={materials['Material.025']} position={[-48.599, 9.477, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[44.895, 0, -109.585]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0007.geometry} material={materials['Material.026']} position={[-48.47, 10.578, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-2.712, 0, -101.319]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0001.geometry} material={materials['Material.029']} position={[0.42, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[0.051, 0, -104.36]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0002.geometry} material={materials['Material.030']} position={[0.42, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-2.709, 0, -107.045]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, 0.001]}>
            <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_Material001_0003.geometry} material={materials['Material.031']} position={[0.42, 1.428, 0]} scale={[1, 1, 1.942]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[-0.272, 0, -109.366]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0004.geometry} material={materials['Material.032']} position={[0.42, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-2.905, 0, -111.942]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0005.geometry} material={materials['Material.033']} position={[0.42, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-0.66, 0, -114.123]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0006.geometry} material={materials['Material.034']} position={[0.42, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-3.066, 0, -114.123]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0007.geometry} material={materials['Material.035']} position={[0.42, 4.105, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[31.249, 0, -101.319]} rotation={[Math.PI / 2, 0, 0]} scale={[-3.751, -1, -1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[333.755, 0, 95.557]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_3_Material001_0001.geometry} material={materials['Material.044']} position={[1.87, -56.091, 0]} scale={[0.709, 17.535, 2.001]} />
          </group>
        </group>
      </group>
      <group position={[26.685, 0, -101.319]} rotation={[Math.PI / 2, 0, 0]} scale={[-3.751, -1, -1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[333.755, 0, 95.557]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_3_Material001_0002.geometry} material={materials['Material.045']} position={[2.063, -23.803, 0]} scale={[0.709, 4.01, 2.001]} />
          </group>
        </group>
      </group>
      <group position={[19.737, 0, -101.319]} rotation={[Math.PI / 2, 0, 0]} scale={[-3.751, -1, -1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[333.755, 0, 95.557]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_3_Material001_0003.geometry} material={materials['Material.046']} position={[1.87, -30.161, 0]} scale={[0.709, 4.302, 2.001]} />
          </group>
        </group>
      </group>
      <group position={[31.249, 0, -101.319]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <mesh geometry={nodes.wall_pillar_Material001_0008.geometry} material={materials['Material.049']} position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100} />
        </group>
      </group>
      <group position={[2.598, 0, -78.836]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -0.002]}>
            <group position={[0, 0, -413.623]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_Material001_0009.geometry} material={materials['Material.050']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[4.775, 0, -78.836]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -0.002]}>
            <group position={[0, 0, -413.623]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_Material001_0010.geometry} material={materials['Material.051']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[4.775, 0, -83.275]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0011.geometry} material={materials['Material.052']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[2.433, 0, -83.275]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0012.geometry} material={materials['Material.053']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[4.775, 0, -75.345]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0013.geometry} material={materials['Material.054']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[4.775, 0, -71.886]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0014.geometry} material={materials['Material.055']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[4.775, 0, -68.342]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.623]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0015.geometry} material={materials['Material.056']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[4.775, 0, -64.713]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0016.geometry} material={materials['Material.057']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[4.775, 0, -61.226]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -0.001]}>
            <group position={[0, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_Material001_0017.geometry} material={materials['Material.058']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[2.598, 0, -75.012]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0018.geometry} material={materials['Material.059']} position={[-1.158, 1.428, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[2.598, 0, -71.653]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -0.002]}>
            <group position={[0, 0, -413.623]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_Material001_0019.geometry} material={materials['Material.060']} position={[-1.158, 1.671, 0]} scale={[1, 1, 1.942]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[2.598, 0, -68.477]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -0.002]}>
            <group position={[0, 0, -413.623]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_Material001_0020.geometry} material={materials['Material.061']} position={[-1.158, 1.671, 0]} scale={[1, 1, 1.942]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[2.598, 0, -64.705]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -0.002]}>
            <group position={[0, 0, -413.623]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_Material001_0021.geometry} material={materials['Material.062']} position={[-1.158, 1.671, 0]} scale={[1, 1, 1.942]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[2.598, 0, -61.156]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0022.geometry} material={materials['Material.063']} position={[-1.158, 1.671, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-3.129, 0, -61.156]} rotation={[-Math.PI / 2, 0, 0]} scale={[0.398, 0.402, 1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0023.geometry} material={materials['Material.071']} position={[-1.158, 1.671, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[44.895, 0, -31.88]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0008.geometry} material={materials['Material.082']} position={[-36.733, 8.856, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[44.895, 0, -25.802]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0009.geometry} material={materials['Material.083']} position={[-36.733, 2.187, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[44.895, 0, -25.802]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0010.geometry} material={materials['Material.084']} position={[-36.733, 8.856, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[44.895, 0, -19.786]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, 0]}>
            <group position={[-204.219, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_thing_Material001_0011.geometry} material={materials['Material.085']} position={[-36.733, 2.187, 0]} scale={[1.585, 1.636, 2.03]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[26.685, 0, -51.135]} rotation={[Math.PI / 2, 0, 0]} scale={[-3.751, -1, -1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[333.755, 0, 95.557]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_3_Material001_0004.geometry} material={materials['Material.086']} position={[3.386, -27.738, 0]} scale={[0.709, 4.836, 2.001]} />
          </group>
        </group>
      </group>
      <group position={[26.685, 0, -69.386]} rotation={[Math.PI / 2, 0, 0]} scale={[-3.751, -1, -1]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[333.755, 0, 95.557]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_3_Material001_0005.geometry} material={materials['Material.090']} position={[3.386, -22.127, 0]} scale={[0.709, 1.782, 2.001]} />
          </group>
        </group>
      </group>
      <group position={[-10.243, 0, -84.934]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, 0.001]}>
            <group position={[-204.22, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_thing_Material001_0012.geometry} material={materials['Material.134']} position={[-48.599, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[-10.243, 0, -78.91]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0013.geometry} material={materials['Material.135']} position={[-48.599, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-10.243, 0, -72.948]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0014.geometry} material={materials['Material.136']} position={[-54.711, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-10.243, 0, -72.948]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0015.geometry} material={materials['Material.137']} position={[-48.599, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-10.243, 0, -78.956]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0016.geometry} material={materials['Material.138']} position={[-54.711, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-16.263, 0, -78.956]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0017.geometry} material={materials['Material.139']} position={[-54.711, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-16.263, 0, -84.878]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0018.geometry} material={materials['Material.140']} position={[-54.711, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-10.457, 0, -84.878]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0019.geometry} material={materials['Material.141']} position={[-54.711, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-22.197, 0, -84.878]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0020.geometry} material={materials['Material.142']} position={[-54.711, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-22.197, 0, -79.176]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, 0]}>
            <group position={[-204.22, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_thing_Material001_0021.geometry} material={materials['Material.143']} position={[-54.711, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[-15.968, 0, -72.553]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0023.geometry} material={materials['Material.145']} position={[-54.711, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-45.991, 0, -75.035]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0024.geometry} material={materials['Material.149']} position={[-0.209, 4.105, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-45.991, 0, -75.035]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0025.geometry} material={materials['Material.150']} position={[-0.23, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-44.648, 0, -78.14]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0026.geometry} material={materials['Material.151']} position={[1.317, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-42.783, 0, -81.298]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0027.geometry} material={materials['Material.152']} position={[2.025, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-42.783, 0, -84.313]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0028.geometry} material={materials['Material.153']} position={[2.025, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-45.386, 0, -84.313]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0029.geometry} material={materials['Material.154']} position={[2.025, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-48.134, 0, -84.313]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0030.geometry} material={materials['Material.155']} position={[2.025, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[12.862, 0, -78.956]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_thing_Material001_0022.geometry} material={materials['Material.156']} position={[-54.711, 16.968, 0]} scale={[1.585, 1.636, 2.03]} />
          </group>
        </group>
      </group>
      <group position={[-45.378, 0, -81.298]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0031.geometry} material={materials['Material.157']} position={[2.025, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-48.198, 0, -81.298]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0032.geometry} material={materials['Material.158']} position={[2.025, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-48.185, 0, -78.14]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0033.geometry} material={materials['Material.159']} position={[2.025, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-43.055, 0, -75.035]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0034.geometry} material={materials['Material.160']} position={[-0.23, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-35.218, 0, -75.035]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0035.geometry} material={materials['Material.165']} position={[-0.23, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-36.223, 0, -66.737]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -413.621]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
            <mesh geometry={nodes.wall_pillar_Material001_0036.geometry} material={materials['Material.166']} position={[-0.23, 7.028, 0]} scale={[1, 1, 1.942]} />
          </group>
        </group>
      </group>
      <group position={[-22.197, 0, -113.343]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -0.001]}>
            <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_thing_Material001_0024.geometry} material={materials['Material.168']} position={[-89.739, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[-22.197, 0, -113.343]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -0.001]}>
            <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_thing_Material001_0025.geometry} material={materials['Material.169']} position={[-89.739, 11.16, 0]} scale={[1.585, 1.636, 2.03]} />
            </group>
          </group>
        </group>
      </group>
      <group position={[-47.918, 0, -113.343]} rotation={[-Math.PI / 2, 0, 0]}>
        <group rotation={[Math.PI / 2, 0, 0]} scale={0.01}>
          <group position={[0, 0, -0.001]}>
            <group position={[-204.22, 0, -413.622]} rotation={[-Math.PI / 2, 0, 0]} scale={100}>
              <mesh geometry={nodes.wall_pillar_thing_Material001_0026.geometry} material={materials['Material.170']} position={[-89.739, 9.785, 0]} scale={[1.585, 1.636, 2.03]} />
            </group>
          </group>
        </group>
      </group>
      <mesh geometry={nodes.Plane.geometry} material={nodes.Plane.material} position={[-46.804, 0, -60.777]} scale={114.921} />
      <mesh geometry={nodes.Cube.geometry} material={nodes.Cube.material} position={[-92.052, 1.909, -69.77]} scale={[0.15, 2.01, 19]} />
      <mesh geometry={nodes.Cube001.geometry} material={nodes.Cube001.material} position={[-83.541, 1.909, -89.393]} scale={[0.15, 2.01, 0.816]} />
      <mesh geometry={nodes.Cube003.geometry} material={nodes.Cube003.material} position={[-85.778, 1.909, -88.673]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.369]} />
      <mesh geometry={nodes.Cube004.geometry} material={nodes.Cube004.material} position={[-83.541, 1.909, -93.715]} scale={[0.15, 2.01, 1.078]} />
      <mesh geometry={nodes.Cube005.geometry} material={nodes.Cube005.material} position={[-82.913, 1.911, -90.095]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 0.622]} />
      <mesh geometry={nodes.Cube006.geometry} material={nodes.Cube006.material} position={[-78.795, 1.909, -91.228]} scale={[0.24, 2.01, 1.23]} />
      <mesh geometry={nodes.Cube007.geometry} material={nodes.Cube007.material} position={[-81.134, 1.911, -92.55]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.568]} />
      <mesh geometry={nodes.Cube008.geometry} material={nodes.Cube008.material} position={[-86.948, 1.911, -94.79]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 3.556]} />
      <mesh geometry={nodes.Column.geometry} material={materials.Column_material} position={[-89.647, 0.023, -91.275]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_bottom.geometry} material={materials.Column_rect} position={[0, -0.05, 0]} />
        <mesh geometry={nodes.Column_box_top.geometry} material={materials.Column_rect} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Cube009.geometry} material={nodes.Cube009.material} position={[-90.33, 1.909, -97.178]} scale={[0.15, 2.01, 2.538]} />
      <mesh geometry={nodes.Cube010.geometry} material={nodes.Cube010.material} position={[-89.269, 1.911, -99.637]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.089]} />
      <mesh geometry={nodes.Cube011.geometry} material={materials['Material.001']} position={[-88.045, 1.909, -104.846]} scale={[0.15, 2.01, 5.241]} />
      <mesh geometry={nodes.Cube012.geometry} material={materials['Material.001']} position={[-77.853, 1.911, -104.328]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 4.201]} />
      <mesh geometry={nodes.Cube013.geometry} material={nodes.Cube013.material} position={[-85.096, 1.911, -109.953]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 3.127]} />
      <mesh geometry={nodes.Cube014.geometry} material={nodes.Cube014.material} position={[-82.101, 1.911, -107.143]} scale={[0.15, 2.01, 2.995]} />
      <mesh geometry={nodes.Cube015.geometry} material={nodes.Cube015.material} position={[-73.782, 1.911, -105.206]} scale={[0.15, 2.01, 1.035]} />
      <mesh geometry={nodes.Cube016.geometry} material={nodes.Cube016.material} position={[-75.971, 1.911, -109.941]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.35]} />
      <mesh geometry={nodes.Cube017.geometry} material={nodes.Cube017.material} position={[-84.678, 1.911, -122.531]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 6.425]} />
      <mesh geometry={nodes.Cube018.geometry} material={nodes.Cube018.material} position={[-78.16, 1.911, -116.232]} scale={[0.15, 2.01, 6.425]} />
      <mesh geometry={nodes.Cube019.geometry} material={nodes.Cube019.material} position={[-91.042, 1.909, -123.636]} scale={[0.15, 2.01, 1.121]} />
      <mesh geometry={nodes.Cube020.geometry} material={nodes.Cube020.material} position={[-94.525, 1.911, -124.558]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 3.556]} />
      <mesh geometry={nodes.Cube021.geometry} material={nodes.Cube021.material} position={[-95.464, 1.827, -88.62]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 3.556]} />
      <mesh geometry={nodes.Cube022.geometry} material={nodes.Cube022.material} position={[-101.277, 1.866, -90.86]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 2.568]} />
      <mesh geometry={nodes.Cube023.geometry} material={nodes.Cube023.material} position={[-103.617, 1.894, -92.182]} rotation={[-3.109, 0, 3.136]} scale={[0.24, 2.01, 1.23]} />
      <mesh geometry={nodes.Cube024.geometry} material={nodes.Cube024.material} position={[-101.277, 1.945, -93.313]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 2.568]} />
      <mesh geometry={nodes.Cube025.geometry} material={nodes.Cube025.material} position={[-98.871, 1.841, -89.695]} rotation={[-3.109, 0, 3.136]} scale={[0.15, 2.01, 1.078]} />
      <mesh geometry={nodes.Cube026.geometry} material={nodes.Cube026.material} position={[-97.646, 2.016, -94.734]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 1.389]} />
      <mesh geometry={nodes.Cube027.geometry} material={nodes.Cube027.material} position={[-98.871, 1.98, -94.015]} rotation={[-3.109, 0, 3.136]} scale={[0.15, 2.01, 0.816]} />
      <mesh geometry={nodes.Cube028.geometry} material={nodes.Cube028.material} position={[-96.41, 1.909, -97.178]} scale={[0.15, 2.01, 2.538]} />
      <mesh geometry={nodes.Cube029.geometry} material={nodes.Cube029.material} position={[-99.051, 1.911, -99.637]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.778]} />
      <mesh geometry={nodes.Cube030.geometry} material={nodes.Cube030.material} position={[-101.586, 1.909, -102.812]} scale={[0.15, 2.01, 3.327]} />
      <mesh geometry={nodes.Cube031.geometry} material={nodes.Cube031.material} position={[-102.361, 2.016, -113.183]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube032.geometry} material={nodes.Cube032.material} position={[-103.139, 2.016, -114.021]} scale={[0.15, 2.01, 0.777]} />
      <mesh geometry={nodes.Cube033.geometry} material={nodes.Cube033.material} position={[-102.58, 2.016, -114.973]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube034.geometry} material={nodes.Cube034.material} position={[-102.015, 1.909, -118.095]} rotation={[0, 0, Math.PI]} scale={[0.15, 2.01, 3.231]} />
      <mesh geometry={nodes.Cube035.geometry} material={nodes.Cube035.material} position={[-91.601, 1.909, -107.654]} scale={[0.15, 2.01, 2.649]} />
      <mesh geometry={nodes.Cube036.geometry} material={nodes.Cube036.material} position={[-97.708, 1.909, -107.654]} scale={[0.15, 2.01, 2.649]} />
      <mesh geometry={nodes.Cube037.geometry} material={nodes.Cube037.material} position={[-94.615, 1.909, -104.993]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 3.001]} />
      <mesh geometry={nodes.Cube038.geometry} material={nodes.Cube038.material} position={[-98.291, 2.016, -110.275]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube039.geometry} material={nodes.Cube039.material} position={[-94.108, 1.909, -111.099]} scale={[0.15, 2.01, 1.058]} />
      <mesh geometry={nodes.Cube040.geometry} material={nodes.Cube040.material} position={[-92.832, 1.909, -110.177]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.149]} />
      <mesh geometry={nodes.Cube041.geometry} material={nodes.Cube041.material} position={[-96.558, 1.909, -112.025]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.455]} />
      <mesh geometry={nodes.Cube042.geometry} material={nodes.Cube042.material} position={[-98.865, 2.016, -111.037]} scale={[0.15, 2.01, 0.8]} />
      <mesh geometry={nodes.Cube043.geometry} material={nodes.Cube043.material} position={[-102.374, 2.016, -107.994]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube044.geometry} material={nodes.Cube044.material} position={[-102.934, 2.016, -107.041]} scale={[0.15, 2.01, 0.777]} />
      <mesh geometry={nodes.Cube045.geometry} material={nodes.Cube045.material} position={[-102.155, 2.016, -106.204]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube046.geometry} material={nodes.Cube046.material} position={[-101.715, 1.909, -110.454]} scale={[0.15, 2.01, 2.711]} />
      <mesh geometry={nodes.Cube047.geometry} material={nodes.Cube047.material} position={[-97.936, 1.909, -122.904]} scale={[0.15, 2.01, 1.76]} />
      <mesh geometry={nodes.Cube048.geometry} material={nodes.Cube048.material} position={[-99.966, 1.911, -121.183]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.053]} />
      <mesh geometry={nodes.Cube049.geometry} material={nodes.Cube049.material} position={[-83.431, 1.909, -113.609]} rotation={[0, -1.564, 0]} scale={[0.15, 2.01, 2.248]} />
      <mesh geometry={nodes.Cube050.geometry} material={nodes.Cube050.material} position={[-84.653, 1.909, -118.803]} rotation={[0, -1.564, 0]} scale={[0.15, 2.01, 3.285]} />
      <mesh geometry={nodes.Cube051.geometry} material={nodes.Cube051.material} position={[-81.308, 1.909, -116.162]} rotation={[Math.PI, -0.006, Math.PI]} scale={[0.15, 2.01, 2.649]} />
      <mesh geometry={nodes.Cube052.geometry} material={nodes.Cube052.material} position={[-85.509, 1.909, -114.698]} rotation={[Math.PI, -0.006, Math.PI]} scale={[0.15, 2.01, 1.175]} />
      <mesh geometry={nodes.Cube053.geometry} material={nodes.Cube053.material} position={[-86.559, 1.909, -115.667]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.175]} />
      <mesh geometry={nodes.Cube054.geometry} material={nodes.Cube054.material} position={[-87.781, 1.909, -117.175]} rotation={[Math.PI, -0.006, Math.PI]} scale={[0.15, 2.01, 1.6]} />
      <mesh geometry={nodes.Cube055.geometry} material={nodes.Cube055.material} position={[-96.244, 1.909, -114.882]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.455]} />
      <mesh geometry={nodes.Cube056.geometry} material={nodes.Cube056.material} position={[-93.968, 2.016, -115.371]} scale={[0.15, 2.01, 0.655]} />
      <mesh geometry={nodes.Cube057.geometry} material={nodes.Cube057.material} position={[-91.965, 1.909, -115.855]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.123]} />
      <mesh geometry={nodes.Cube058.geometry} material={nodes.Cube058.material} position={[-89.908, 2.016, -116.439]} scale={[0.15, 2.01, 0.655]} />
      <mesh geometry={nodes.Cube059.geometry} material={nodes.Cube059.material} position={[-94.259, 1.827, -117.121]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 4.48]} />
      <mesh geometry={nodes.Cube060.geometry} material={nodes.Cube060.material} position={[-98.594, 2.016, -115.936]} scale={[0.15, 2.01, 1.081]} />
      <mesh geometry={nodes.Column001.geometry} material={materials['Column_material.001']} position={[-79.301, 0.023, -109.846]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_bottom001.geometry} material={materials['Column_rect.001']} position={[0, -0.05, 0]} />
        <mesh geometry={nodes.Column_box_top001.geometry} material={materials['Column_rect.001']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Cube061.geometry} material={nodes.Cube061.material} position={[-73.782, 1.911, -109.075]} scale={[0.15, 2.01, 1.066]} />
      <mesh geometry={nodes.Cube062.geometry} material={nodes.Cube062.material} position={[-69.425, 1.911, -106.057]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 4.137]} />
      <mesh geometry={nodes.Cube063.geometry} material={nodes.Cube063.material} position={[-66.515, 1.911, -108.149]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 7.207]} />
      <mesh geometry={nodes.Cube064.geometry} material={nodes.Cube064.material} position={[-53.846, 3.027, -114.726]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 3.071, 5.612]} />
      <mesh geometry={nodes.Cube065.geometry} material={nodes.Cube065.material} position={[-59.401, 2.998, -111.485]} scale={[0.15, 3.121, 3.371]} />
      <mesh geometry={nodes.Cube067.geometry} material={nodes.Cube067.material} position={[-53.657, 0.363, -104.441]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 0.364, 5.612]} />
      <mesh geometry={nodes.Cube068.geometry} material={nodes.Cube068.material} position={[-53.846, -7.045, -100.522]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 13.373]} />
      <mesh geometry={nodes.Cube070.geometry} material={nodes.Cube070.material} position={[-57.749, 2.998, -111.485]} scale={[0.15, 3.121, 3.371]} />
      <mesh geometry={nodes.Cube071.geometry} material={nodes.Cube071.material} position={[-56.168, 2.998, -111.485]} scale={[0.15, 3.121, 3.371]} />
      <mesh geometry={nodes.Cube072.geometry} material={nodes.Cube072.material} position={[-54.536, 2.998, -111.485]} scale={[0.15, 3.121, 3.371]} />
      <mesh geometry={nodes.Cube073.geometry} material={nodes.Cube073.material} position={[-52.934, 2.998, -111.485]} scale={[0.15, 3.121, 3.371]} />
      <mesh geometry={nodes.Cube074.geometry} material={nodes.Cube074.material} position={[-51.295, 2.998, -111.485]} scale={[0.15, 3.121, 3.371]} />
      <mesh geometry={nodes.Cube075.geometry} material={nodes.Cube075.material} position={[-49.677, 2.998, -111.485]} scale={[0.15, 3.121, 3.371]} />
      <mesh geometry={nodes.Cube076.geometry} material={nodes.Cube076.material} position={[-48.133, 2.998, -111.485]} scale={[0.15, 3.121, 3.371]} />
      <mesh geometry={nodes.Cube077.geometry} material={nodes.Cube077.material} position={[-47.888, -7.299, -100.336]} scale={[0.15, 13.45, 4.228]} />
      <mesh geometry={nodes.Cube078.geometry} material={nodes.Cube078.material} position={[-53.763, 3.051, -96.018]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 3.104, 5.948]} />
      <mesh geometry={nodes.Plane001.geometry} material={nodes.Plane001.material} position={[-53.8, 6.119, -105.505]} scale={[5.797, 1, 9.371]} />
      <mesh geometry={nodes.Cube069.geometry} material={nodes.Cube069.material} position={[-53.763, -2.949, -96.018]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 3.104, 5.948]} />
      <mesh geometry={nodes.Cube079.geometry} material={nodes.Cube079.material} position={[-53.763, -8.949, -96.018]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 3.104, 5.948]} />
      <mesh geometry={nodes.Cube080.geometry} material={nodes.Cube080.material} position={[-53.763, -13.949, -96.018]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 3.104, 5.948]} />
      <mesh geometry={nodes.Cube081.geometry} material={nodes.Cube081.material} position={[-53.483, -20.538, -99.769]} rotation={[1.561, Math.PI / 2, 0]} scale={[0.15, 4.393, 6.236]} />
      <mesh geometry={nodes.Cube082.geometry} material={nodes.Cube082.material} position={[-53.763, -19.933, -96.018]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 3.104, 5.948]} />
      <mesh geometry={nodes.Cube066.geometry} material={nodes.Cube066.material} position={[-59.355, -7.299, -101.182]} scale={[0.15, 13.45, 4.892]} />
      <mesh geometry={nodes.Cube083.geometry} material={materials['Material.002']} position={[-43.564, 1.911, -104.413]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 4.201]} />
      <mesh geometry={nodes.Cube084.geometry} material={materials['Material.003']} position={[-41.79, 1.911, -108.229]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 6.424]} />
      <mesh geometry={nodes.Cube085.geometry} material={nodes.Cube085.material} position={[-39.537, 1.909, -97.903]} scale={[0.24, 2.01, 6.691]} />
      <mesh geometry={nodes.Cube086.geometry} material={nodes.Cube086.material} position={[-35.624, 1.909, -101.762]} scale={[0.24, 2.01, 6.669]} />
      <mesh geometry={nodes.Cube087.geometry} material={materials['Material.004']} position={[-30.342, 1.911, -91.283]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 3.505]} />
      <mesh geometry={nodes.Cube088.geometry} material={materials['Material.005']} position={[-35.03, 1.911, -95.26]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 0.67]} />
      <mesh geometry={nodes.Cube089.geometry} material={nodes.Cube089.material} position={[-26.092, 4.446, -86.369]} scale={[0.24, 4.6, 4.961]} />
      <mesh geometry={nodes.Cube090.geometry} material={nodes.Cube090.material} position={[-26.092, 4.446, -100.202]} scale={[0.24, 4.6, 4.961]} />
      <mesh geometry={nodes.Cube091.geometry} material={nodes.Cube091.material} position={[-4.63, 4.446, -93.051]} scale={[0.24, 4.6, 11.653]} />
      <mesh geometry={nodes.Cube092.geometry} material={nodes.Cube092.material} position={[-15.317, 4.446, -104.908]} rotation={[0, Math.PI / 2, 0]} scale={[0.24, 4.6, 11.008]} />
      <mesh geometry={nodes.Cube093.geometry} material={nodes.Cube093.material} position={[-8.406, 4.446, -81.53]} rotation={[0, Math.PI / 2, 0]} scale={[0.24, 4.6, 3.718]} />
      <mesh geometry={nodes.Cube094.geometry} material={nodes.Cube094.material} position={[-22.483, 4.446, -81.53]} rotation={[0, Math.PI / 2, 0]} scale={[0.24, 4.6, 3.832]} />
      <mesh geometry={nodes.Cube095.geometry} material={nodes.Cube095.material} />
      <mesh geometry={nodes.Cube096.geometry} material={nodes.Cube096.material} position={[-7.644, 10.361, -107.498]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube097.geometry} material={nodes.Cube097.material} position={[-11.628, 10.361, -107.498]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube098.geometry} material={nodes.Cube098.material} position={[-15.786, 10.361, -107.498]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube099.geometry} material={nodes.Cube099.material} position={[-19.86, 10.361, -107.498]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube100.geometry} material={nodes.Cube100.material} position={[-24.292, 10.361, -107.498]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube101.geometry} material={nodes.Cube101.material} position={[-28.243, 10.361, -103.066]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube102.geometry} material={nodes.Cube102.material} position={[-28.243, 10.361, -98.014]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube103.geometry} material={nodes.Cube103.material} position={[-28.243, 10.361, -88.989]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube104.geometry} material={nodes.Cube104.material} position={[-28.243, 10.361, -83.302]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube105.geometry} material={nodes.Cube105.material} position={[-2.525, 10.361, -103.066]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube106.geometry} material={nodes.Cube106.material} position={[-2.525, 10.361, -98.533]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube107.geometry} material={nodes.Cube107.material} position={[-2.525, 10.361, -93.112]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube108.geometry} material={nodes.Cube108.material} position={[-2.525, 10.361, -87.688]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Cube109.geometry} material={nodes.Cube109.material} position={[-2.525, 10.361, -82.533]} rotation={[Math.PI / 2, 0, -Math.PI / 2]} scale={[0.255, 0.371, 2.371]} />
      <mesh geometry={nodes.Plane002.geometry} material={nodes.Plane002.material} position={[-12.721, 12.616, -96.588]} scale={[18.656, 1, 15.169]} />
      <mesh geometry={nodes.Cube110.geometry} material={nodes.Cube110.material} position={[-11.994, 1.909, -62.683]} scale={[0.15, 2.01, 19]} />
      <mesh geometry={nodes.Cube111.geometry} material={nodes.Cube111.material} position={[-18.895, 1.909, -62.683]} scale={[0.15, 2.01, 19]} />
      <mesh geometry={nodes.Cube112.geometry} material={nodes.Cube112.material} position={[-23.208, 1.447, -43.79]} scale={[4.213, 1.594, 0.052]} />
      <mesh geometry={nodes.Cube113.geometry} material={nodes.Cube113.material} position={[-11.711, 1.94, -43.79]} scale={[4.213, 1.923, 0.052]} />
      <mesh geometry={nodes.Baseboard.geometry} material={materials.Baseboard_material} />
      <mesh geometry={nodes.Baseboard001.geometry} material={materials.Baseboard_material} />
      <mesh geometry={nodes.Cube002.geometry} material={materials['Material.008']} position={[-25.442, 1.193, -72.603]} scale={[0.15, 1.343, 8.562]} />
      <mesh geometry={nodes.Cube114.geometry} material={materials['Material.007']} position={[-41.79, 1.911, -114.503]} scale={[0.15, 2.01, 6.424]} />
      <mesh geometry={nodes.Cube115.geometry} material={materials['Material.009']} position={[-45.82, 1.911, -114.503]} scale={[0.15, 2.01, 6.424]} />
      <mesh geometry={nodes.Cube116.geometry} material={materials['Material.010']} position={[-70.474, 1.911, -114.503]} scale={[0.15, 2.01, 6.424]} />
      <mesh geometry={nodes.Cube117.geometry} material={materials['Material.011']} position={[-67.651, 1.911, -114.503]} scale={[0.15, 2.01, 6.424]} />
      <mesh geometry={nodes.Cube118.geometry} material={materials['Material.012']} position={[-56.875, 1.911, -120.963]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 10.816]} />
      <mesh geometry={nodes.Cube119.geometry} material={materials['Material.013']} position={[-53, 1.911, -125.904]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 20.007]} />
      <mesh geometry={nodes.Cube120.geometry} material={nodes.Cube120.material} position={[-37.493, 1.911, -119.182]} scale={[0.15, 2.01, 2.113]} />
      <mesh geometry={nodes.Cube121.geometry} material={nodes.Cube121.material} position={[-39.673, 1.911, -121.184]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.285]} />
      <mesh geometry={nodes.Cube122.geometry} material={nodes.Cube122.material} position={[-31.299, 1.911, -119.182]} scale={[0.15, 2.01, 2.113]} />
      <mesh geometry={nodes.Cube123.geometry} material={nodes.Cube123.material} position={[-34.493, 1.911, -116.493]} scale={[0.15, 2.01, 2.113]} />
      <mesh geometry={nodes.Cube124.geometry} material={nodes.Cube124.material} position={[-29.225, 1.911, -118.41]} scale={[0.15, 2.01, 2.113]} />
      <mesh geometry={nodes.Cube126.geometry} material={nodes.Cube126.material} position={[-38.431, 1.911, -109.966]} scale={[0.15, 2.01, 2.113]} />
      <mesh geometry={nodes.Cube127.geometry} material={nodes.Cube127.material} position={[-35.087, 1.911, -114.462]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 0.689]} />
      <mesh geometry={nodes.Cube128.geometry} material={nodes.Cube128.material} position={[-33.762, 1.911, -108.048]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 0.672]} />
      <mesh geometry={nodes.Cube129.geometry} material={nodes.Cube129.material} position={[-30.018, 1.911, -109.599]} scale={[0.15, 2.01, 3.289]} />
      <mesh geometry={nodes.Cube130.geometry} material={nodes.Cube130.material} position={[-27.461, 1.911, -109.966]} scale={[0.15, 2.01, 5.071]} />
      <mesh geometry={nodes.Cube131.geometry} material={nodes.Cube131.material} position={[-34.489, 1.911, -101.769]} scale={[0.15, 2.01, 6.451]} />
      <mesh geometry={nodes.Cube132.geometry} material={nodes.Cube132.material} position={[-33.053, 1.911, -109.966]} scale={[0.15, 2.01, 2.113]} />
      <mesh geometry={nodes.Cube133.geometry} material={materials['Material.014']} position={[-31.541, 1.911, -112.009]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.518]} />
      <mesh geometry={nodes.Cube134.geometry} material={materials['Material.015']} position={[-32.923, 1.911, -117.224]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.518]} />
      <mesh geometry={nodes.Cube125.geometry} material={materials['Material.016']} position={[-27.85, 1.911, -120.456]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.518]} />
      <mesh geometry={nodes.Cube135.geometry} material={materials['Material.017']} position={[-26.216, 1.911, -114.946]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.518]} />
      <mesh geometry={nodes.Cube136.geometry} material={nodes.Cube136.material} position={[-24.789, 1.911, -113.305]} scale={[0.15, 2.01, 1.713]} />
      <mesh geometry={nodes.Cube137.geometry} material={nodes.Cube137.material} position={[-33.196, 1.911, -127.851]} scale={[0.15, 2.01, 2.113]} />
      <mesh geometry={nodes.Cube138.geometry} material={nodes.Cube138.material} position={[-28.979, 1.911, -122.73]} scale={[0.15, 2.01, 2.113]} />
      <mesh geometry={nodes.Cube139.geometry} material={materials['Material.018']} position={[-17.214, 1.911, -129.771]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 15.986]} />
      <mesh geometry={nodes.Cube140.geometry} material={nodes.Cube140.material} position={[-1.16, 1.911, -126.508]} scale={[0.15, 2.01, 3.414]} />
      <mesh geometry={nodes.Cube141.geometry} material={materials['Material.027']} position={[0.418, 1.911, -123.247]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.518]} />
      <mesh geometry={nodes.Cube142.geometry} material={nodes.Cube142.material} position={[1.852, 1.911, -128.622]} scale={[0.15, 2.01, 5.444]} />
      <mesh geometry={nodes.Cube143.geometry} material={materials['Material.028']} position={[3.801, 1.911, -133.924]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube144.geometry} material={nodes.Cube144.material} position={[5.565, 1.911, -128.622]} scale={[0.15, 2.01, 5.444]} />
      <mesh geometry={nodes.Cube145.geometry} material={nodes.Cube145.material} position={[1.852, 1.911, -106.174]} scale={[0.15, 2.01, 5.444]} />
      <mesh geometry={nodes.Cube146.geometry} material={materials['Material.036']} position={[3.72, 1.911, -100.873]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube147.geometry} material={nodes.Cube147.material} position={[5.544, 1.911, -98.535]} scale={[0.15, 2.01, 2.487]} />
      <mesh geometry={nodes.Cube148.geometry} material={materials['Material.037']} position={[2.926, 1.911, -96.186]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.593]} />
      <mesh geometry={nodes.Cube149.geometry} material={materials['Material.038']} position={[3.247, 1.911, -91.844]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.939]} />
      <mesh geometry={nodes.Cube150.geometry} material={materials['Material.039']} position={[7.04, 1.911, -123.247]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.518]} />
      <mesh geometry={nodes.Cube151.geometry} material={nodes.Cube151.material} position={[8.48, 1.911, -117.84]} scale={[0.15, 2.01, 5.444]} />
      <mesh geometry={nodes.Cube152.geometry} material={materials['Material.040']} position={[7.04, 1.911, -112.554]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.518]} />
      <mesh geometry={nodes.Cube153.geometry} material={nodes.Cube153.material} position={[5.836, 1.911, -109.442]} scale={[0.15, 2.01, 3.189]} />
      <mesh geometry={nodes.Cube154.geometry} material={materials['Material.041']} position={[7.694, 1.911, -100.901]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube155.geometry} material={materials['Material.042']} position={[10.631, 1.911, -106.386]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 4.915]} />
      <mesh geometry={nodes.Cube156.geometry} material={nodes.Cube156.material} position={[15.383, 1.911, -101.013]} scale={[0.15, 2.01, 5.444]} />
      <mesh geometry={nodes.Cube157.geometry} material={materials['Material.043']} position={[13.44, 1.911, -95.772]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube158.geometry} material={nodes.Cube158.material} position={[12.986, 1.911, -98.191]} scale={[0.15, 2.01, 2.487]} />
      <mesh geometry={nodes.Cube159.geometry} material={nodes.Cube159.material} position={[11.633, 1.911, -93.272]} scale={[0.15, 2.01, 2.487]} />
      <mesh geometry={nodes.Cube160.geometry} material={materials['Material.047']} position={[10.631, 1.911, -106.386]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 4.915]} />
      <mesh geometry={nodes.Cube161.geometry} material={materials['Material.065']} position={[-1.607, 1.911, -58.848]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube162.geometry} material={materials['Material.066']} position={[-3.449, 1.911, -56.905]} rotation={[0, -0.02, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube164.geometry} material={materials['Material.068']} position={[-7.187, 1.911, -54.573]} rotation={[0, -0.018, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube163.geometry} material={materials['Material.069']} position={[-7.022, 1.911, -61.706]} rotation={[0, -0.02, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube165.geometry} material={materials['Material.070']} position={[-5.879, 1.911, -66.842]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 6.193]} />
      <mesh geometry={nodes.Cube166.geometry} material={materials['Material.072']} position={[-7.187, 1.911, -48.338]} rotation={[0, -0.018, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube167.geometry} material={materials['Material.073']} position={[-9.543, 1.911, -59.889]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.517]} />
      <mesh geometry={nodes.Cube168.geometry} material={materials['Material.074']} position={[-9.543, 1.911, -56.389]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.517]} />
      <mesh geometry={nodes.Cube169.geometry} material={materials['Material.075']} position={[8.975, 1.911, -62.014]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.939]} />
      <mesh geometry={nodes.Cube170.geometry} material={materials['Material.076']} position={[7.81, 1.911, -66.003]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.332]} />
      <mesh geometry={nodes.Cube171.geometry} material={materials['Material.077']} position={[2.129, 1.911, -58.848]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube172.geometry} material={materials['Material.078']} position={[3.943, 1.911, -56.905]} rotation={[0, -0.02, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube173.geometry} material={materials['Material.079']} position={[7.495, 1.911, -60.12]} rotation={[0, -0.02, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube174.geometry} material={materials['Material.080']} position={[2.129, 1.911, -55.023]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube175.geometry} material={materials['Material.081']} position={[2.129, 1.911, -51.095]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.981]} />
      <mesh geometry={nodes.Cube176.geometry} material={materials['Material.087']} position={[8.919, 1.911, -20.499]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.939]} />
      <mesh geometry={nodes.Cube177.geometry} material={materials['Material.088']} position={[4.404, 1.911, -15.466]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.939]} />
      <mesh geometry={nodes.Cube178.geometry} material={materials['Material.089']} position={[8.919, 1.911, -10.462]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.939]} />
      <mesh geometry={nodes.Cube179.geometry} material={materials['Material.091']} position={[-15.162, 1.911, -16.226]} rotation={[0, -0.012, 0]} scale={[0.15, 2.01, 2.939]} />
      <mesh geometry={nodes.Cube180.geometry} material={materials['Material.092']} position={[-11.545, 1.911, -16.226]} rotation={[0, -0.012, 0]} scale={[0.15, 2.01, 2.939]} />
      <mesh geometry={nodes.Cube181.geometry} material={materials['Material.093']} position={[-7.119, 1.911, -15.466]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 4.458]} />
      <mesh geometry={nodes.Cube182.geometry} material={materials['Material.094']} position={[-2.849, 1.911, -16.226]} rotation={[0, -0.012, 0]} scale={[0.15, 2.01, 2.939]} />
      <mesh geometry={nodes.Cube183.geometry} material={materials['Material.095']} position={[-1.273, 1.911, -9.58]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.939]} />
      <mesh geometry={nodes.Cube184.geometry} material={materials['Material.096']} position={[-13.623, 1.911, 2.528]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 25.415]} />
      <mesh geometry={nodes.Cube185.geometry} material={nodes.Cube185.material} position={[-18.895, 1.909, -62.683]} scale={[0.15, 2.01, 19]} />
      <mesh geometry={nodes.Cube186.geometry} material={nodes.Cube186.material} position={[-87.951, 1.909, -69.77]} scale={[0.15, 2.01, 19]} />
      <mesh geometry={nodes.Cube187.geometry} material={materials['Material.098']} position={[-97.657, 2.2, -19.878]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.247, 3.373]} />
      <mesh geometry={nodes.Cube188.geometry} material={materials['Material.099']} position={[-98.018, 2.2, -17.195]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.247, 11.937]} />
      <mesh geometry={nodes.Cube189.geometry} material={materials['Material.100']} position={[-94.369, 1.909, -25.183]} scale={[0.15, 2.01, 5.241]} />
      <mesh geometry={nodes.Cube190.geometry} material={materials['Material.101']} position={[-94.369, 1.909, -35.568]} scale={[0.15, 2.01, 1.697]} />
      <mesh geometry={nodes.Cube191.geometry} material={materials['Material.102']} position={[-92.727, 1.909, -30.241]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.697]} />
      <mesh geometry={nodes.Cube192.geometry} material={materials['Material.103']} position={[-91.153, 1.909, -28.636]} scale={[0.15, 2.01, 1.697]} />
      <mesh geometry={nodes.Cube193.geometry} material={materials['Material.104']} position={[-86.349, 1.909, -20.699]} scale={[0.15, 2.01, 3.525]} />
      <mesh geometry={nodes.Cube194.geometry} material={materials['Material.105']} position={[-85.821, 1.909, -24.069]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 0.404]} />
      <mesh geometry={nodes.Cube195.geometry} material={materials['Material.106']} position={[-86.834, 1.909, -27.237]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.338]} />
      <mesh geometry={nodes.Cube196.geometry} material={materials['Material.107']} position={[-88.395, 1.909, -33.549]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube197.geometry} material={materials['Material.108']} position={[-80.665, 1.909, -52.166]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube198.geometry} material={materials['Material.109']} position={[-80.743, 1.909, -50.248]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube199.geometry} material={materials['Material.110']} position={[-80.629, 1.909, -55]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube200.geometry} material={materials['Material.111']} position={[-80.548, 1.909, -56.939]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube201.geometry} material={materials['Material.112']} position={[-80.555, 1.909, -59.666]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube202.geometry} material={materials['Material.113']} position={[-80.446, 1.909, -61.695]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube203.geometry} material={materials['Material.114']} position={[-80.464, 1.909, -64.526]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube204.geometry} material={materials['Material.115']} position={[-80.398, 1.909, -66.549]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube205.geometry} material={materials['Material.116']} position={[-82.723, 1.909, -69.183]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 5.05]} />
      <mesh geometry={nodes.Cube206.geometry} material={materials['Material.117']} position={[-80.351, 1.909, -70.987]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube207.geometry} material={materials['Material.118']} position={[-82.737, 1.909, -73.709]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 5.234]} />
      <mesh geometry={nodes.Cube208.geometry} material={materials['Material.119']} position={[-80.888, 1.909, -75.853]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 3.419]} />
      <mesh geometry={nodes.Cube209.geometry} material={materials['Material.120']} position={[-80.743, 1.909, -50.248]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube210.geometry} material={materials['Material.121']} position={[-72.59, 1.909, -50.248]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube211.geometry} material={materials['Material.122']} position={[-67.341, 1.909, -75.769]} rotation={[0, Math.PI / 2, 0]} scale={[0.151, 2.01, 7.463]} />
      <mesh geometry={nodes.Cube212.geometry} material={materials['Material.123']} position={[-72.09, 1.909, -73.709]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube213.geometry} material={materials['Material.124']} position={[-72.223, 1.909, -70.987]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube214.geometry} material={materials['Material.125']} position={[-72.255, 1.909, -69.183]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube215.geometry} material={materials['Material.126']} position={[-72.32, 1.909, -66.549]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube216.geometry} material={materials['Material.127']} position={[-72.356, 1.909, -64.526]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube217.geometry} material={materials['Material.128']} position={[-72.386, 1.909, -61.695]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube218.geometry} material={materials['Material.129']} position={[-72.415, 1.909, -59.666]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube219.geometry} material={materials['Material.130']} position={[-72.455, 1.909, -56.939]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube220.geometry} material={materials['Material.131']} position={[-72.46, 1.909, -55]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube221.geometry} material={materials['Material.132']} position={[-72.512, 1.909, -52.166]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube222.geometry} material={nodes.Cube222.material} position={[-84.134, 1.911, -78.517]} scale={[0.15, 2.01, 2.568]} />
      <mesh geometry={nodes.Cube223.geometry} material={nodes.Cube223.material} position={[-82.513, 1.909, -87.975]} scale={[0.193, 2.01, 2.089]} />
      <mesh geometry={nodes.Cube224.geometry} material={nodes.Cube224.material} position={[-61.1, 1.911, -106.057]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.854]} />
      <mesh geometry={nodes.Cube225.geometry} material={nodes.Cube225.material} position={[-75.775, 1.911, -80.891]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 8.441]} />
      <mesh geometry={nodes.Cube226.geometry} material={nodes.Cube226.material} position={[-76.373, 1.911, -90.147]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.568]} />
      <mesh geometry={nodes.Cube227.geometry} material={materials['Material.133']} position={[-65.586, 1.909, -100.855]} scale={[0.15, 2.01, 5.241]} />
      <mesh geometry={nodes.Cube228.geometry} material={nodes.Cube228.material} position={[-55.543, 1.911, -80.891]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 8.441]} />
      <mesh geometry={nodes.Cube229.geometry} material={materials['Material.146']} position={[-59.725, 1.909, -73.643]} scale={[0.15, 2.01, 2.189]} />
      <mesh geometry={nodes.Cube230.geometry} material={materials['Material.147']} position={[-56.648, 1.909, -73.959]} scale={[0.15, 2.01, 2.675]} />
      <mesh geometry={nodes.Cube231.geometry} material={materials['Material.148']} position={[-49.263, 1.909, -76.416]} rotation={[0, Math.PI / 2, 0]} scale={[0.151, 2.01, 7.463]} />
      <mesh geometry={nodes.Cube232.geometry} material={materials['Material.161']} position={[-38.272, 1.911, -91.283]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.337]} />
      <mesh geometry={nodes.Cube233.geometry} material={materials['Material.162']} position={[-39.683, 1.909, -89.091]} scale={[0.15, 2.01, 2.34]} />
      <mesh geometry={nodes.Cube234.geometry} material={materials['Material.163']} position={[-30.051, 1.909, -74.245]} rotation={[0, Math.PI / 2, 0]} scale={[0.151, 2.01, 4.557]} />
      <mesh geometry={nodes.Cube235.geometry} material={materials['Material.164']} position={[-37.841, 1.909, -82.473]} rotation={[0, Math.PI / 2, 0]} scale={[0.151, 2.01, 4.557]} />
      <mesh geometry={nodes.Cube236.geometry} material={materials['Material.167']} position={[-72.935, 1.911, -130.443]} scale={[0.15, 2.01, 4.542]} />
      <mesh geometry={nodes.Cube237.geometry} material={nodes.Cube237.material} position={[-79.179, 1.911, -134.877]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 6.425]} />
      <mesh geometry={nodes.Cube238.geometry} material={nodes.Cube238.material} position={[-83.099, 1.911, -129.308]} rotation={[0, Math.PI / 2, 0]} scale={[0.195, 2.01, 6.425]} />
      <mesh geometry={nodes.Cube239.geometry} material={nodes.Cube239.material} position={[-72.423, 1.911, -121.116]} rotation={[0, Math.PI / 2, 0]} scale={[0.195, 2.01, 2.111]} />
      <mesh geometry={nodes.Cube240.geometry} material={nodes.Cube240.material} position={[-85.92, 1.909, -127.846]} rotation={[Math.PI, -0.006, Math.PI]} scale={[0.15, 2.01, 1.6]} />
      <mesh geometry={nodes.Cube241.geometry} material={nodes.Cube241.material} position={[-80.728, 1.909, -124.196]} rotation={[Math.PI, -0.006, Math.PI]} scale={[0.15, 2.01, 1.6]} />
      <mesh geometry={nodes.Cube242.geometry} material={nodes.Cube242.material} position={[-108.523, 1.911, -134.877]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 33.064]} />
      <mesh geometry={nodes.Cube243.geometry} material={nodes.Cube243.material} position={[-134.072, 1.911, -129.308]} rotation={[0, Math.PI / 2, 0]} scale={[0.195, 2.01, 6.425]} />
      <mesh geometry={nodes.Cube244.geometry} material={nodes.Cube244.material} position={[-127.838, 1.909, -126.185]} scale={[0.15, 2.01, 3.231]} />
      <mesh geometry={nodes.Cube245.geometry} material={nodes.Cube245.material} position={[-125.06, 1.909, -126.246]} scale={[0.15, 2.01, 3.231]} />
      <mesh geometry={nodes.Cube246.geometry} material={nodes.Cube246.material} position={[-116.94, 1.827, -130.843]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 4.48]} />
      <mesh geometry={nodes.Cube247.geometry} material={nodes.Cube247.material} position={[-118.925, 1.909, -128.604]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.455]} />
      <mesh geometry={nodes.Cube248.geometry} material={nodes.Cube248.material} position={[-108.917, 1.909, -128.165]} rotation={[Math.PI, -0.006, Math.PI]} scale={[0.15, 2.01, 1.6]} />
      <mesh geometry={nodes.Cube249.geometry} material={nodes.Cube249.material} position={[-102.444, 1.909, -127.152]} rotation={[Math.PI, -0.006, Math.PI]} scale={[0.15, 2.01, 2.649]} />
      <mesh geometry={nodes.Cube250.geometry} material={nodes.Cube250.material} position={[-104.567, 1.909, -124.598]} rotation={[0, -1.564, 0]} scale={[0.15, 2.01, 2.248]} />
      <mesh geometry={nodes.Cube251.geometry} material={nodes.Cube251.material} position={[-107.878, 2.051, -110.454]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 2.711]} />
      <mesh geometry={nodes.Cube252.geometry} material={nodes.Cube252.material} position={[-107.438, 1.945, -106.204]} rotation={[-1.597, 1.564, -1.577]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube253.geometry} material={nodes.Cube253.material} position={[-106.659, 1.946, -107.041]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 0.777]} />
      <mesh geometry={nodes.Cube254.geometry} material={nodes.Cube254.material} position={[-107.219, 1.945, -107.994]} rotation={[-1.597, 1.564, -1.577]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube255.geometry} material={nodes.Cube255.material} position={[-107.577, 2.052, -118.095]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 3.231]} />
      <mesh geometry={nodes.Cube256.geometry} material={nodes.Cube256.material} position={[-107.013, 1.945, -114.973]} rotation={[-1.597, 1.564, -1.577]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube257.geometry} material={nodes.Cube257.material} position={[-106.453, 1.946, -114.021]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 0.777]} />
      <mesh geometry={nodes.Cube258.geometry} material={nodes.Cube258.material} position={[-107.232, 1.945, -113.183]} rotation={[-1.597, 1.564, -1.577]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube259.geometry} material={nodes.Cube259.material} position={[-108.007, 2.051, -102.812]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 3.327]} />
      <mesh geometry={nodes.Cube260.geometry} material={nodes.Cube260.material} position={[-121.506, 2.051, -102.812]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 3.327]} />
      <mesh geometry={nodes.Cube261.geometry} material={nodes.Cube261.material} position={[-120.731, 1.945, -113.183]} rotation={[-1.597, 1.564, -1.577]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube262.geometry} material={nodes.Cube262.material} position={[-119.953, 1.946, -114.021]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 0.777]} />
      <mesh geometry={nodes.Cube263.geometry} material={nodes.Cube263.material} position={[-120.513, 1.945, -114.973]} rotation={[-1.597, 1.564, -1.577]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube264.geometry} material={nodes.Cube264.material} position={[-121.077, 2.052, -118.095]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 3.231]} />
      <mesh geometry={nodes.Cube265.geometry} material={nodes.Cube265.material} position={[-120.718, 1.945, -107.994]} rotation={[-1.597, 1.564, -1.577]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube266.geometry} material={nodes.Cube266.material} position={[-120.159, 1.946, -107.041]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 0.777]} />
      <mesh geometry={nodes.Cube267.geometry} material={nodes.Cube267.material} position={[-120.937, 1.945, -106.204]} rotation={[-1.597, 1.564, -1.577]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube268.geometry} material={nodes.Cube268.material} position={[-121.378, 2.051, -110.454]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 2.711]} />
      <mesh geometry={nodes.Cube269.geometry} material={nodes.Cube269.material} position={[-115.272, 1.909, -110.454]} scale={[0.15, 2.01, 2.711]} />
      <mesh geometry={nodes.Cube270.geometry} material={nodes.Cube270.material} position={[-115.713, 2.016, -106.204]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube271.geometry} material={nodes.Cube271.material} position={[-115.573, 1.909, -118.095]} rotation={[0, 0, Math.PI]} scale={[0.15, 2.01, 3.231]} />
      <mesh geometry={nodes.Cube272.geometry} material={nodes.Cube272.material} position={[-116.137, 2.016, -114.973]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube273.geometry} material={nodes.Cube273.material} position={[-116.697, 2.016, -114.021]} scale={[0.15, 2.01, 0.777]} />
      <mesh geometry={nodes.Cube274.geometry} material={nodes.Cube274.material} position={[-115.918, 2.016, -113.183]} rotation={[1.603, -1.565, 1.571]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube275.geometry} material={nodes.Cube275.material} position={[-115.144, 1.909, -102.812]} scale={[0.15, 2.01, 3.327]} />
      <mesh geometry={nodes.Column002.geometry} material={materials['Column_material.002']} position={[-114.157, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top002.geometry} material={materials['Column_rect.002']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column003.geometry} material={materials['Column_material.003']} position={[-113.032, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top003.geometry} material={materials['Column_rect.003']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column004.geometry} material={materials['Column_material.004']} position={[-111.955, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top004.geometry} material={materials['Column_rect.004']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column005.geometry} material={materials['Column_material.005']} position={[-110.977, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top005.geometry} material={materials['Column_rect.005']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column006.geometry} material={materials['Column_material.006']} position={[-110.101, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top006.geometry} material={materials['Column_rect.006']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column007.geometry} material={materials['Column_material.007']} position={[-109.192, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top007.geometry} material={materials['Column_rect.007']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column008.geometry} material={materials['Column_material.008']} position={[-108.261, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top008.geometry} material={materials['Column_rect.008']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column009.geometry} material={materials['Column_material.009']} position={[-115.128, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top009.geometry} material={materials['Column_rect.009']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column010.geometry} material={materials['Column_material.010']} position={[-115.128, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top010.geometry} material={materials['Column_rect.010']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column011.geometry} material={materials['Column_material.011']} position={[-108.261, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top011.geometry} material={materials['Column_rect.011']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column012.geometry} material={materials['Column_material.012']} position={[-109.192, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top012.geometry} material={materials['Column_rect.012']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column013.geometry} material={materials['Column_material.013']} position={[-110.101, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top013.geometry} material={materials['Column_rect.013']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column014.geometry} material={materials['Column_material.014']} position={[-110.977, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top014.geometry} material={materials['Column_rect.014']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column015.geometry} material={materials['Column_material.015']} position={[-111.955, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top015.geometry} material={materials['Column_rect.015']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column016.geometry} material={materials['Column_material.016']} position={[-113.032, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top016.geometry} material={materials['Column_rect.016']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column017.geometry} material={materials['Column_material.017']} position={[-114.157, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top017.geometry} material={materials['Column_rect.017']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column018.geometry} material={materials['Column_material.018']} position={[-114.157, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top018.geometry} material={materials['Column_rect.018']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column019.geometry} material={materials['Column_material.019']} position={[-113.032, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top019.geometry} material={materials['Column_rect.019']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column020.geometry} material={materials['Column_material.020']} position={[-111.955, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top020.geometry} material={materials['Column_rect.020']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column021.geometry} material={materials['Column_material.021']} position={[-110.977, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top021.geometry} material={materials['Column_rect.021']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column022.geometry} material={materials['Column_material.022']} position={[-110.101, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top022.geometry} material={materials['Column_rect.022']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column023.geometry} material={materials['Column_material.023']} position={[-109.192, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top023.geometry} material={materials['Column_rect.023']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column024.geometry} material={materials['Column_material.024']} position={[-108.261, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top024.geometry} material={materials['Column_rect.024']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column025.geometry} material={materials['Column_material.025']} position={[-115.128, 0.023, -119.921]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top025.geometry} material={materials['Column_rect.025']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column026.geometry} material={materials['Column_material.026']} position={[-115.128, 0.023, -117.367]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top026.geometry} material={materials['Column_rect.026']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column027.geometry} material={materials['Column_material.027']} position={[-108.261, 0.023, -117.367]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top027.geometry} material={materials['Column_rect.027']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column028.geometry} material={materials['Column_material.028']} position={[-115.128, 0.023, -117.367]} scale={[1, 1.714, 1]} />
      <mesh geometry={nodes.Column029.geometry} material={materials['Column_material.029']} position={[-108.261, 0.023, -117.367]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top028.geometry} material={materials['Column_rect.028']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column030.geometry} material={materials['Column_material.030']} position={[-109.192, 0.023, -117.367]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top029.geometry} material={materials['Column_rect.029']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column031.geometry} material={materials['Column_material.031']} position={[-110.101, 0.023, -117.367]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top030.geometry} material={materials['Column_rect.030']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column032.geometry} material={materials['Column_material.032']} position={[-110.977, 0.023, -117.367]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top031.geometry} material={materials['Column_rect.031']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column033.geometry} material={materials['Column_material.033']} position={[-111.955, 0.023, -117.367]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top032.geometry} material={materials['Column_rect.032']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column034.geometry} material={materials['Column_material.034']} position={[-113.032, 0.023, -117.367]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top033.geometry} material={materials['Column_rect.033']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column035.geometry} material={materials['Column_material.035']} position={[-114.157, 0.023, -117.367]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top034.geometry} material={materials['Column_rect.034']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column036.geometry} material={materials['Column_material.036']} position={[-114.157, 0.023, -114.786]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top035.geometry} material={materials['Column_rect.035']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column037.geometry} material={materials['Column_material.037']} position={[-113.032, 0.023, -114.786]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top036.geometry} material={materials['Column_rect.036']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column038.geometry} material={materials['Column_material.038']} position={[-111.955, 0.023, -114.786]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top037.geometry} material={materials['Column_rect.037']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column039.geometry} material={materials['Column_material.039']} position={[-110.977, 0.023, -114.786]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top038.geometry} material={materials['Column_rect.038']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column040.geometry} material={materials['Column_material.040']} position={[-110.101, 0.023, -114.786]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top039.geometry} material={materials['Column_rect.039']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column041.geometry} material={materials['Column_material.041']} position={[-109.192, 0.023, -114.786]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top040.geometry} material={materials['Column_rect.040']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column042.geometry} material={materials['Column_material.042']} position={[-108.261, 0.023, -114.786]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top041.geometry} material={materials['Column_rect.041']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column043.geometry} material={materials['Column_material.043']} position={[-115.128, 0.023, -114.786]} scale={[1, 1.714, 1]} />
      <mesh geometry={nodes.Column044.geometry} material={materials['Column_material.044']} position={[-108.261, 0.023, -114.786]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top042.geometry} material={materials['Column_rect.042']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column045.geometry} material={materials['Column_material.045']} position={[-115.128, 0.023, -114.786]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top043.geometry} material={materials['Column_rect.043']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column046.geometry} material={materials['Column_material.046']} position={[-115.128, 0.023, -104.993]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top044.geometry} material={materials['Column_rect.044']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column047.geometry} material={materials['Column_material.047']} position={[-108.261, 0.023, -104.993]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top045.geometry} material={materials['Column_rect.045']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column048.geometry} material={materials['Column_material.048']} position={[-115.128, 0.023, -104.993]} scale={[1, 1.714, 1]} />
      <mesh geometry={nodes.Column049.geometry} material={materials['Column_material.049']} position={[-108.261, 0.023, -104.993]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top046.geometry} material={materials['Column_rect.046']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column050.geometry} material={materials['Column_material.050']} position={[-109.192, 0.023, -104.993]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top047.geometry} material={materials['Column_rect.047']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column051.geometry} material={materials['Column_material.051']} position={[-110.101, 0.023, -104.993]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top048.geometry} material={materials['Column_rect.048']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column052.geometry} material={materials['Column_material.052']} position={[-110.977, 0.023, -104.993]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top049.geometry} material={materials['Column_rect.049']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column053.geometry} material={materials['Column_material.053']} position={[-111.955, 0.023, -104.993]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top050.geometry} material={materials['Column_rect.050']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column054.geometry} material={materials['Column_material.054']} position={[-113.032, 0.023, -104.993]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top051.geometry} material={materials['Column_rect.051']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column055.geometry} material={materials['Column_material.055']} position={[-114.157, 0.023, -104.993]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top052.geometry} material={materials['Column_rect.052']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column056.geometry} material={materials['Column_material.056']} position={[-114.157, 0.023, -103.199]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top053.geometry} material={materials['Column_rect.053']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column057.geometry} material={materials['Column_material.057']} position={[-113.032, 0.023, -103.199]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top054.geometry} material={materials['Column_rect.054']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column058.geometry} material={materials['Column_material.058']} position={[-111.955, 0.023, -103.199]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top055.geometry} material={materials['Column_rect.055']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column059.geometry} material={materials['Column_material.059']} position={[-110.977, 0.023, -103.199]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top056.geometry} material={materials['Column_rect.056']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column060.geometry} material={materials['Column_material.060']} position={[-110.101, 0.023, -103.199]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top057.geometry} material={materials['Column_rect.057']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column061.geometry} material={materials['Column_material.061']} position={[-109.192, 0.023, -103.199]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top058.geometry} material={materials['Column_rect.058']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column062.geometry} material={materials['Column_material.062']} position={[-108.261, 0.023, -103.199]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top059.geometry} material={materials['Column_rect.059']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column063.geometry} material={materials['Column_material.063']} position={[-115.128, 0.023, -103.199]} scale={[1, 1.714, 1]} />
      <mesh geometry={nodes.Column064.geometry} material={materials['Column_material.064']} position={[-108.261, 0.023, -103.199]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top060.geometry} material={materials['Column_rect.060']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column065.geometry} material={materials['Column_material.065']} position={[-115.128, 0.023, -103.199]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top061.geometry} material={materials['Column_rect.061']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column066.geometry} material={materials['Column_material.066']} position={[-114.157, 0.023, -101.294]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top062.geometry} material={materials['Column_rect.062']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column067.geometry} material={materials['Column_material.067']} position={[-113.032, 0.023, -101.294]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top063.geometry} material={materials['Column_rect.063']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column068.geometry} material={materials['Column_material.068']} position={[-111.955, 0.023, -101.294]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top064.geometry} material={materials['Column_rect.064']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column069.geometry} material={materials['Column_material.069']} position={[-110.977, 0.023, -101.294]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top065.geometry} material={materials['Column_rect.065']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column070.geometry} material={materials['Column_material.070']} position={[-110.101, 0.023, -101.294]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top066.geometry} material={materials['Column_rect.066']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column071.geometry} material={materials['Column_material.071']} position={[-109.192, 0.023, -101.294]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top067.geometry} material={materials['Column_rect.067']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column072.geometry} material={materials['Column_material.072']} position={[-108.261, 0.023, -101.294]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top068.geometry} material={materials['Column_rect.068']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column073.geometry} material={materials['Column_material.073']} position={[-115.128, 0.023, -101.294]} scale={[1, 1.714, 1]} />
      <mesh geometry={nodes.Column074.geometry} material={materials['Column_material.074']} position={[-108.261, 0.023, -101.294]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top069.geometry} material={materials['Column_rect.069']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column075.geometry} material={materials['Column_material.075']} position={[-115.128, 0.023, -101.294]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top070.geometry} material={materials['Column_rect.070']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column076.geometry} material={materials['Column_material.076']} position={[-115.128, 0.023, -99.629]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top071.geometry} material={materials['Column_rect.071']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column077.geometry} material={materials['Column_material.077']} position={[-108.261, 0.023, -99.629]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top072.geometry} material={materials['Column_rect.072']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column078.geometry} material={materials['Column_material.078']} position={[-115.128, 0.023, -99.629]} scale={[1, 1.714, 1]} />
      <mesh geometry={nodes.Column079.geometry} material={materials['Column_material.079']} position={[-108.261, 0.023, -99.629]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top073.geometry} material={materials['Column_rect.073']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column080.geometry} material={materials['Column_material.080']} position={[-109.192, 0.023, -99.629]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top074.geometry} material={materials['Column_rect.074']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column081.geometry} material={materials['Column_material.081']} position={[-110.101, 0.023, -99.629]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top075.geometry} material={materials['Column_rect.075']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column082.geometry} material={materials['Column_material.082']} position={[-110.977, 0.023, -99.629]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top076.geometry} material={materials['Column_rect.076']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column083.geometry} material={materials['Column_material.083']} position={[-111.955, 0.023, -99.629]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top077.geometry} material={materials['Column_rect.077']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column084.geometry} material={materials['Column_material.084']} position={[-113.032, 0.023, -99.629]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top078.geometry} material={materials['Column_rect.078']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Column085.geometry} material={materials['Column_material.085']} position={[-114.157, 0.023, -99.629]} scale={[1, 1.714, 1]}>
        <mesh geometry={nodes.Column_box_top079.geometry} material={materials['Column_rect.079']} position={[0, 2.3, 0]} />
      </mesh>
      <mesh geometry={nodes.Cube276.geometry} material={nodes.Cube276.material} position={[-115.849, 1.945, -107.994]} rotation={[-1.597, 1.564, -1.577]} scale={[0.15, 2.01, 0.709]} />
      <mesh geometry={nodes.Cube277.geometry} material={nodes.Cube277.material} position={[-116.536, 1.946, -107.041]} rotation={[0, 0, -3.14]} scale={[0.15, 2.01, 0.777]} />
      <mesh geometry={nodes.Cube278.geometry} material={nodes.Cube278.material} position={[-121.048, 1.909, -121.191]} rotation={[0, -1.564, 0]} scale={[0.15, 2.01, 1.929]} />
      <mesh geometry={nodes.Cube279.geometry} material={nodes.Cube279.material} position={[-121.262, 1.909, -127.209]} rotation={[Math.PI, 0, Math.PI]} scale={[0.15, 2.01, 1.6]} />
      <mesh geometry={nodes.Cube280.geometry} material={nodes.Cube280.material} position={[-98.865, 2.016, -133.717]} scale={[0.15, 2.01, 0.8]} />
      <mesh geometry={nodes.Cube281.geometry} material={nodes.Cube281.material} position={[-96.558, 1.909, -134.705]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.455]} />
      <mesh geometry={nodes.Cube282.geometry} material={nodes.Cube282.material} position={[-92.832, 1.909, -132.856]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 1.149]} />
      <mesh geometry={nodes.Cube283.geometry} material={nodes.Cube283.material} position={[-94.108, 1.909, -133.779]} scale={[0.15, 2.01, 1.058]} />
      <mesh geometry={nodes.Cube284.geometry} material={nodes.Cube284.material} position={[-97.708, 1.909, -130.334]} scale={[0.15, 2.01, 2.649]} />
      <mesh geometry={nodes.Cube285.geometry} material={nodes.Cube285.material} position={[-91.601, 1.909, -130.334]} scale={[0.15, 2.01, 2.649]} />
      <mesh geometry={nodes.Cube286.geometry} material={nodes.Cube286.material} position={[-142.004, 2.105, -125.113]} rotation={[Math.PI, -1.535, Math.PI]} scale={[0.15, 2.01, 1.6]} />
      <mesh geometry={nodes.Cube287.geometry} material={nodes.Cube287.material} position={[-143.298, 2.105, -127.507]} rotation={[-Math.PI, 0.042, -Math.PI]} scale={[0.15, 2.01, 2.455]} />
      <mesh geometry={nodes.Column086.geometry} material={materials['Column_material.086']} position={[-142.861, 0.023, -133.508]} scale={[2.202, 1.714, 2.374]} />
      <mesh geometry={nodes.Column087.geometry} material={materials['Column_material.087']} position={[-141.861, 0.023, -133.508]} rotation={[0.424, 0, 0]} scale={[2.202, 1.714, 2.374]} />
      <mesh geometry={nodes.Column088.geometry} material={materials['Column_material.088']} position={[-141.861, 0.023, -133.508]} rotation={[0.337, -0.263, -0.638]} scale={[2.202, 1.714, 2.374]} />
      <mesh geometry={nodes.Column089.geometry} material={materials['Column_material.089']} position={[-141.861, 0.023, -133.508]} rotation={[-0.326, -0.263, -0.638]} scale={[2.202, 1.714, 2.374]} />
      <mesh geometry={nodes.Column090.geometry} material={materials['Column_material.090']} position={[-141.861, 0.023, -133.508]} rotation={[1.32, -0.263, -0.638]} scale={[2.202, 1.714, 2.374]} />
      <mesh geometry={nodes.Column091.geometry} material={materials['Column_material.091']} position={[-142.861, 0.023, -133.508]} rotation={[1.337, 0, 0]} scale={[2.202, 1.714, 2.374]} />
      <mesh geometry={nodes.Column092.geometry} material={materials['Column_material.092']} position={[-142.861, 2.023, -133.508]} rotation={[1.936, 0, 0]} scale={[2.202, 1.714, 2.374]} />
      <mesh geometry={nodes.Column093.geometry} material={materials['Column_material.093']} position={[-142.861, 2.023, -133.508]} rotation={[1.141, 0, 0]} scale={[2.202, 1.714, 2.374]} />
      <mesh geometry={nodes.Column094.geometry} material={materials['Column_material.094']} position={[-142.861, 2.023, -133.508]} rotation={[-2.114, 0, 0]} scale={[2.202, 1.714, 2.374]} />
      <mesh geometry={nodes.Cube288.geometry} material={nodes.Cube288.material} position={[-146.298, 2.105, -135.507]} rotation={[Math.PI, 0, Math.PI]} scale={[0.15, 2.01, 2.455]} />
      <mesh geometry={nodes.Cube289.geometry} material={nodes.Cube289.material} position={[-146.004, 2.105, -131.113]} rotation={[Math.PI, -1.535, Math.PI]} scale={[2.024, 2.01, 1.701]} />
      <mesh geometry={nodes.Cube290.geometry} material={nodes.Cube290.material} position={[-144.298, 2.105, -135.507]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 2.455]} />
      <mesh geometry={nodes.Cube291.geometry} material={nodes.Cube291.material} position={[-112.485, 1.841, -89.427]} rotation={[-0.724, 1.522, 0.718]} scale={[0.15, 2.01, 1.078]} />
      <mesh geometry={nodes.Cube292.geometry} material={nodes.Cube292.material} position={[-109.827, 1.894, -94.079]} rotation={[-0.724, 1.522, 0.718]} scale={[0.24, 2.01, 1.23]} />
      <mesh geometry={nodes.Cube293.geometry} material={nodes.Cube293.material} position={[-111.233, 1.866, -91.789]} rotation={[3.136, 0.037, 3.11]} scale={[0.15, 2.01, 2.568]} />
      <mesh geometry={nodes.Cube294.geometry} material={nodes.Cube294.material} position={[-113.684, 1.827, -86.061]} rotation={[3.136, 0.037, 3.11]} scale={[0.15, 2.01, 3.556]} />
      <mesh geometry={nodes.Cube295.geometry} material={nodes.Cube295.material} position={[-110.684, 1.827, -81.061]} rotation={[3.136, 0.037, 3.11]} scale={[0.15, 2.01, 3.556]} />
      <mesh geometry={nodes.Cube296.geometry} material={nodes.Cube296.material} position={[-108.233, 1.866, -86.789]} rotation={[3.136, 0.037, 3.11]} scale={[0.15, 2.01, 2.568]} />
      <mesh geometry={nodes.Cube297.geometry} material={nodes.Cube297.material} position={[-106.827, 1.894, -89.079]} rotation={[-0.724, 1.522, 0.718]} scale={[0.24, 2.01, 1.23]} />
      <mesh geometry={nodes.Cube298.geometry} material={nodes.Cube298.material} position={[-109.485, 1.841, -84.427]} rotation={[-0.724, 1.522, 0.718]} scale={[0.15, 2.01, 1.078]} />
      <mesh geometry={nodes.Cube299.geometry} material={nodes.Cube299.material} position={[-97.173, 1.841, -66.282]} rotation={[-1.252, -1.537, -1.258]} scale={[0.15, 2.01, 1.078]} />
      <mesh geometry={nodes.Cube300.geometry} material={nodes.Cube300.material} position={[-99.609, 1.894, -61.51]} rotation={[-1.252, -1.537, -1.258]} scale={[0.24, 2.01, 1.23]} />
      <mesh geometry={nodes.Cube301.geometry} material={nodes.Cube301.material} position={[-98.312, 1.866, -63.863]} rotation={[0.006, 0.01, -0.032]} scale={[0.15, 2.01, 2.568]} />
      <mesh geometry={nodes.Cube302.geometry} material={nodes.Cube302.material} position={[-96.134, 1.827, -69.701]} rotation={[0.006, 0.01, -0.032]} scale={[0.15, 2.01, 3.556]} />
      <mesh geometry={nodes.Cube303.geometry} material={nodes.Cube303.material} position={[-101.004, 2.105, -83.113]} rotation={[Math.PI, -1.535, Math.PI]} scale={[2.024, 2.01, 1.701]} />
      <mesh geometry={nodes.Cube304.geometry} material={nodes.Cube304.material} position={[-101.004, 2.105, -75.113]} rotation={[Math.PI, -1.535, Math.PI]} scale={[2.024, 2.01, 1.701]} />
      <mesh geometry={nodes.Cube305.geometry} material={nodes.Cube305.material} position={[-109.004, 2.105, -75.113]} rotation={[Math.PI, -1.535, Math.PI]} scale={[2.024, 2.01, 1.701]} />
      <mesh geometry={nodes.Cube306.geometry} material={nodes.Cube306.material} position={[-109.004, 2.105, -68.113]} rotation={[Math.PI, -1.535, Math.PI]} scale={[2.024, 2.01, 1.701]} />
      <mesh geometry={nodes.Cube307.geometry} material={nodes.Cube307.material} position={[-101.004, 4.105, -68.113]} rotation={[2.58, -1.535, Math.PI]} scale={[2.024, 2.01, 1.701]} />
      <mesh geometry={nodes.Cube308.geometry} material={nodes.Cube308.material} position={[-129.684, 1.827, -82.061]} rotation={[3.136, 0.037, 3.11]} scale={[0.15, 2.01, 3.556]} />
      <mesh geometry={nodes.Cube309.geometry} material={nodes.Cube309.material} position={[-126.684, 1.827, -85.061]} rotation={[3.136, 0.037, 3.11]} scale={[0.15, 2.01, 1.064]} />
      <mesh geometry={nodes.Cube310.geometry} material={nodes.Cube310.material} position={[-126.684, 1.827, -81.061]} rotation={[3.136, 0.037, 3.11]} scale={[0.15, 2.01, 1.064]} />
      <mesh geometry={nodes.Cube311.geometry} material={nodes.Cube311.material} position={[-115.881, 1.827, -85.221]} rotation={[0.006, 0.006, -0.032]} scale={[0.15, 2.01, 4.48]} />
      <mesh geometry={nodes.Cube312.geometry} material={nodes.Cube312.material} position={[-117.626, 1.909, -94.816]} rotation={[0, 0.013, 0]} scale={[0.15, 2.01, 3.285]} />
      <mesh geometry={nodes.Cube314.geometry} material={nodes.Cube314.material} position={[-123.315, 1.911, -84.906]} rotation={[Math.PI, -0.007, Math.PI]} scale={[0.15, 2.01, 3.556]} />
      <mesh geometry={nodes.Cube315.geometry} material={nodes.Cube315.material} position={[-121.354, 1.911, -94.766]} rotation={[Math.PI, -0.007, Math.PI]} scale={[0.15, 2.01, 6.425]} />
      <mesh geometry={nodes.Column095.geometry} material={materials['Column_material.095']} position={[-121.229, 0.023, -93.508]} rotation={[0, 0, -Math.PI / 2]} scale={[2.202, 1.589, 2.374]} />
      <mesh geometry={nodes.Column096.geometry} material={materials['Column_material.096']} position={[-121.229, 1.582, -93.508]} rotation={[0, 0, -Math.PI / 2]} scale={[2.202, 1.589, 2.374]} />
      <mesh geometry={nodes.Column097.geometry} material={materials['Column_material.097']} position={[-116.051, 1.582, -83.774]} rotation={[0, 0, -Math.PI / 2]} scale={[2.202, 0.955, 2.374]} />
      <mesh geometry={nodes.Column098.geometry} material={materials['Column_material.098']} position={[-117.081, -0.091, -84.056]} scale={[2.202, 1.589, 2.374]} />
      <mesh geometry={nodes.Column099.geometry} material={materials['Column_material.099']} position={[-118.548, -0.091, -84.056]} scale={[2.202, 1.589, 2.374]} />
      <mesh geometry={nodes.Column100.geometry} material={materials['Column_material.100']} position={[-120.2, -0.091, -84.056]} scale={[2.202, 1.589, 2.374]} />
      <mesh geometry={nodes.Column101.geometry} material={materials['Column_material.101']} position={[-117.081, -0.091, -85.852]} scale={[2.202, 1.589, 2.374]} />
      <mesh geometry={nodes.Cube313.geometry} material={materials['Material.048']} position={[-118.857, 1.909, -29.673]} rotation={[0, Math.PI / 2, 0]} scale={[0.15, 2.01, 6.476]} />
      <mesh geometry={nodes.Column102.geometry} material={materials['Column_material.102']} position={[-124.684, 1.254, -33.68]} rotation={[0, 0, -1.578]} scale={[2.202, 1.589, 2.374]} />
      <mesh geometry={nodes.Cube316.geometry} material={nodes.Cube316.material} position={[-127.329, 1.841, -46.922]} rotation={[-0.724, 1.522, 0.718]} scale={[0.15, 2.01, 1.078]} />
      <mesh geometry={nodes.Cube317.geometry} material={nodes.Cube317.material} position={[-124.671, 1.894, -51.574]} rotation={[-0.724, 1.522, 0.718]} scale={[0.24, 2.01, 1.23]} />
      <mesh geometry={nodes.Cube318.geometry} material={nodes.Cube318.material} position={[-126.077, 1.866, -49.284]} rotation={[3.136, 0.037, 3.11]} scale={[0.15, 2.01, 2.568]} />
      <mesh geometry={nodes.Cube319.geometry} material={nodes.Cube319.material} position={[-128.528, 1.827, -43.556]} rotation={[3.136, 0.037, 3.11]} scale={[0.15, 2.01, 3.556]} />
      <mesh geometry={nodes.Cube320.geometry} material={nodes.Cube320.material} position={[-126.764, 1.894, -40.238]} rotation={[-0.724, 1.522, 0.718]} scale={[0.24, 2.012, 2.048]} />
      <mesh geometry={nodes.Column103.geometry} material={materials['Column_material.103']} position={[-116.051, 0.703, -83.774]} rotation={[0, 0, -Math.PI / 2]} scale={[2.202, 0.955, 2.374]} />
      <mesh geometry={nodes.Column104.geometry} material={materials['Column_material.104']} position={[-116.051, 2.443, -83.774]} rotation={[0, 0, -Math.PI / 2]} scale={[2.202, 0.955, 2.374]} />
      <mesh geometry={nodes.Cube321.geometry} material={nodes.Cube321.material} position={[-41.378, -0.721, -79.916]} scale={[0.075, 1, 1]} />
      <mesh geometry={nodes.Cube322.geometry} material={materials.Material} position={[-56.767, 6.133, -107.157]} rotation={[0, 1.535, 0]} scale={[-0.433, -0.081, -1.147]} />
      <mesh geometry={nodes.Cube323.geometry} material={materials['Material.064']} position={[-51.408, 6.133, -106.934]} rotation={[0, 1.535, 0]} scale={[-0.433, -0.081, -1.147]} />
      <mesh geometry={nodes.Cube324.geometry} material={materials['Material.067']} position={[-51.408, 6.133, -99.852]} rotation={[0, 1.535, 0]} scale={[-0.433, -0.081, -1.147]} />
      <mesh geometry={nodes.Cube325.geometry} material={materials['Material.097']} position={[-57.012, 6.133, -99.852]} rotation={[0, 1.535, 0]} scale={[-0.433, -0.081, -1.147]} />
      </group>
    </group>
  )
}

useGLTF.preload('/poc2.glb')
