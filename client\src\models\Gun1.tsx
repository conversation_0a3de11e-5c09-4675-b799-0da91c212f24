/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.5.3 ./public/gun1.glb 
Author: <PERSON><PERSON><PERSON><PERSON> (https://sketchfab.com/bumstrum)
License: CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)
Source: https://sketchfab.com/3d-models/animated-pistol-bd896167e7ca44f19597d3afe6a8d83f
Title: animated pistol
Converted to TypeScript
*/

import React from "react";
import { useGraph } from "@react-three/fiber";
import { useGLTF } from "@react-three/drei";
import { SkeletonUtils } from "three-stdlib";
import * as THREE from "three";
import { GLTF } from "three-stdlib";

type GLTFResult = GLTF & {
  nodes: {
    [key: string]: THREE.Mesh | THREE.Bone | THREE.Object3D;
    stopper_beretta_0: THREE.Mesh;
    bullet_2_2_beretta_0: THREE.Mesh;
    shell_beretta_0: THREE.Mesh;
    bullet_beretta_0: THREE.Mesh;
    shell_1_2_beretta_0: THREE.Mesh;
    mag_beretta_0: THREE.Mesh;
    hammer_beretta_0: THREE.Mesh;
    trigger_beretta_0: THREE.Mesh;
    slide_beretta_0: THREE.Mesh;
    base_beretta_0: THREE.Mesh;
    bullet_2_beretta_0: THREE.Mesh;
    shell_1_beretta_0: THREE.Mesh;
    _rootJoint: THREE.Bone;
    Object_83: THREE.SkinnedMesh;
  };
  materials: {
    [key: string]: THREE.Material;
    beretta: THREE.Material;
    arms: THREE.Material;
  };
};

interface ModelProps {
  [key: string]: any;
}

export function Model(props: ModelProps): JSX.Element {
  const group = React.useRef<THREE.Group>(null);
  const { scene }: GLTF = useGLTF("/gun1.glb");
  const clone: THREE.Object3D = React.useMemo(
    () => SkeletonUtils.clone(scene),
    [scene]
  );
  const { nodes, materials }: GLTFResult = useGraph(clone) as GLTFResult;
  //   const { actions } = useAnimations(animations, group);

  return (
    <group ref={group} {...props} dispose={null}>
      <group name="Sketchfab_Scene">
        <group name="Sketchfab_model" rotation={[-Math.PI / 2, 0, 0]}>
          <group
            name="105d2810571c4501ba07ac55c5582245fbx"
            rotation={[Math.PI / 2, 0, 0]}
            scale={0.01}
          >
            <group name="Object_2">
              <group name="RootNode">
                <group name="Root" position={[0, 16.327, -8.923]}>
                  <group name="Object_5">
                    <group name="pistol" position={[0, -12.318, 25.913]}>
                      <group name="stopper" position={[1.26, 5.502, 5.615]}>
                        <mesh
                          name="stopper_beretta_0"
                          geometry={nodes.stopper_beretta_0.geometry}
                          material={materials.beretta}
                        />
                      </group>
                      <group
                        name="mag"
                        position={[0, -0.379, 0.406]}
                        rotation={[0.262, 0, 0]}
                      >
                        <group
                          name="shell"
                          position={[0, 7.4, -0.455]}
                          rotation={[-0.262, 0, 0]}
                        >
                          <group name="bullet_2_2" position={[0, 0, 0.935]}>
                            <mesh
                              name="bullet_2_2_beretta_0"
                              geometry={nodes.bullet_2_2_beretta_0.geometry}
                              material={materials.beretta}
                            />
                          </group>
                          <mesh
                            name="shell_beretta_0"
                            geometry={nodes.shell_beretta_0.geometry}
                            material={materials.beretta}
                          />
                        </group>
                        <group
                          name="shell_1_2"
                          position={[0, 6.293, -0.158]}
                          rotation={[-0.262, 0, 0]}
                        >
                          <group name="bullet" position={[0, 0, 0.935]}>
                            <mesh
                              name="bullet_beretta_0"
                              geometry={nodes.bullet_beretta_0.geometry}
                              material={materials.beretta}
                            />
                          </group>
                          <mesh
                            name="shell_1_2_beretta_0"
                            geometry={nodes.shell_1_2_beretta_0.geometry}
                            material={materials.beretta}
                          />
                        </group>
                        <mesh
                          name="mag_beretta_0"
                          geometry={nodes.mag_beretta_0.geometry}
                          material={materials.beretta}
                        />
                      </group>
                      <group
                        name="hammer"
                        position={[0, 5.573, -1.608]}
                        rotation={[-1.242, 0, 0]}
                      >
                        <mesh
                          name="hammer_beretta_0"
                          geometry={nodes.hammer_beretta_0.geometry}
                          material={materials.beretta}
                        />
                      </group>
                      <group name="trigger" position={[0, 4.404, 6.033]}>
                        <mesh
                          name="trigger_beretta_0"
                          geometry={nodes.trigger_beretta_0.geometry}
                          material={materials.beretta}
                        />
                      </group>
                      <group name="slide" position={[0, 7.341, 3.872]}>
                        <mesh
                          name="slide_beretta_0"
                          geometry={nodes.slide_beretta_0.geometry}
                          material={materials.beretta}
                        />
                      </group>
                      <group name="base">
                        <mesh
                          name="base_beretta_0"
                          geometry={nodes.base_beretta_0.geometry}
                          material={materials.beretta}
                        />
                      </group>
                      <group name="shell_1" position={[0, 7.849, 6.937]}>
                        <group name="bullet_2" position={[0, 0, 0.935]}>
                          <mesh
                            name="bullet_2_beretta_0"
                            geometry={nodes.bullet_2_beretta_0.geometry}
                            material={materials.beretta}
                          />
                        </group>
                        <mesh
                          name="shell_1_beretta_0"
                          geometry={nodes.shell_1_beretta_0.geometry}
                          material={materials.beretta}
                        />
                      </group>
                    </group>
                    <primitive object={nodes._rootJoint} />
                    <group name="Object_82" position={[0, 3.036, -1.334]} />
                    <skinnedMesh
                      name="Object_83"
                      geometry={nodes.Object_83.geometry}
                      material={materials.arms}
                      skeleton={nodes.Object_83.skeleton}
                    />
                  </group>
                </group>
                <group
                  name="armsmesh"
                  position={[0, 3.036, -1.334]}
                  rotation={[-0.002, 0, -0.011]}
                />
              </group>
            </group>
          </group>
        </group>
      </group>
    </group>
  );
}

useGLTF.preload("/gun1.glb");
