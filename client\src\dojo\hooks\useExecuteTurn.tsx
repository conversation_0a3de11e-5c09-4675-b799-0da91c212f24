import { useState, useCallback } from "react";
import { useAccount } from "@starknet-react/core";
import { dojoConfig } from "../dojoConfig";

// Define Action type based on the contract structure
interface Action {
  // This would be based on your Action enum from the contract
  type: string;
  data: any;
}

interface UseExecuteTurnReturn {
  executeTurn: (actions: Action[]) => Promise<void>;
  isLoading: boolean;
  error: Error | null;
  resetError: () => void;
}

export const useExecuteTurn = (): UseExecuteTurnReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const { account } = useAccount();

  const executeTurn = useCallback(async (actions: Action[]) => {
    if (!account) {
      setError(new Error("No account connected"));
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${dojoConfig.toriiUrl}/graphql`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: `
            mutation ExecuteTurn($actions: [Action!]!) {
              executeTurn(actions: $actions)
            }
          `,
          variables: {
            actions: actions.map(action => ({
              type: action.type,
              data: action.data,
            })),
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      console.log("✅ Turn executed successfully:", result.data);
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Failed to execute turn");
      console.error("❌ Error executing turn:", error);
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [account]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    executeTurn,
    isLoading,
    error,
    resetError,
  };
}; 