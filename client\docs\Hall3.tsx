/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.5.3 ./public/hall3.glb 
Modified for Backrooms experience with wall textures
Converted to TypeScript
*/

import { useGLTF, useTexture } from "@react-three/drei";
import * as THREE from "three";
import { GLTF } from "three-stdlib";

type GLTFResult = GLTF & {
  nodes: {
    [key: string]: THREE.Mesh;
  };
  materials: {
    [key: string]: THREE.Material;
    "Material.002": THREE.Material;
    pillars: THREE.Material;
  };
};

interface ModelProps {
  [key: string]: any;
}

export function Model(props: ModelProps): JSX.Element {
  const { nodes, materials } = useGLTF("/hall3.glb") as GLTFResult;
  const wallTexture: THREE.Texture = useTexture("./walls.png");

  // Configure wall texture for Backrooms aesthetic
  wallTexture.wrapS = wallTexture.wrapT = THREE.RepeatWrapping;
  wallTexture.repeat.set(10, 26);
  wallTexture.minFilter = THREE.LinearFilter;
  wallTexture.magFilter = THREE.LinearFilter;

  // Create enhanced materials with the wall texture
  const wallMaterial: THREE.MeshLambertMaterial = new THREE.MeshLambertMaterial(
    {
      map: wallTexture,
      color: "#f7f3d0",
    }
  );

  const ceilingMaterial: THREE.MeshLambertMaterial =
    new THREE.MeshLambertMaterial({
      map: wallTexture,
      color: "#f0ead6",
    });

  const floorMaterial: THREE.MeshLambertMaterial =
    new THREE.MeshLambertMaterial({
      map: wallTexture,
      color: "#e8dcc0",
    });

  return (
    <group {...props} dispose={null}>
      {/* Point lights with adjusted intensity for Backrooms atmosphere */}
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, -79.602]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, -79.602]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, -60.007]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, -40.04]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, -20.151]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, -0.312]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, 19.728]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, 39.533]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, 59.538]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, 59.538]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[5.003, 12.505, 79.66]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, -79.602]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, -60.233]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, -40.098]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, -20.173]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, -0.264]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, 19.886]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, 39.57]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, 59.748]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-3.139, 12.505, 79.364]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, 87.671]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, 67.987]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, 49.531]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, 14.278]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, 5.475]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, -6.82]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, -13.816]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, -29.367]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, -69.714]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[-18.952, 12.505, -89.601]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 12.505, -89.601]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 12.505, -68.719]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 12.505, -47.839]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 12.505, -28.703]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 12.505, -13.531]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 12.505, -4.959]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 13.521, 28.253]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 13.521, 49.072]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 13.521, 69.322]}
        rotation={[-Math.PI / 2, 0, 0]}
      />
      <pointLight
        intensity={40}
        decay={2}
        color="#e9c03b"
        position={[20.903, 13.521, 88.235]}
        rotation={[-Math.PI / 2, 0, 0]}
      />

      {/* Wall meshes with textured materials */}
      <mesh
        geometry={nodes.Cube002.geometry}
        material={wallMaterial}
        position={[-16.537, 6.099, 100]}
        scale={[13.573, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube004.geometry}
        material={wallMaterial}
        position={[19.977, 6.099, 80]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube005.geometry}
        material={wallMaterial}
        position={[-16.537, 6.099, -100]}
        scale={[13.573, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube007.geometry}
        material={ceilingMaterial}
        position={[-0.02, 10.724, -100]}
        scale={[3.163, 1.4, 0.235]}
      />
      <mesh
        geometry={nodes.Cube006.geometry}
        material={ceilingMaterial}
        position={[-0.02, 10.724, 100]}
        scale={[3.163, 1.4, 0.235]}
      />
      <mesh
        geometry={nodes.Cube019.geometry}
        material={wallMaterial}
        position={[16.539, 6.099, 100]}
        scale={[13.573, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube001.geometry}
        material={wallMaterial}
        position={[-29.901, 6.099, -0.054]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[100.51, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube003.geometry}
        material={wallMaterial}
        position={[16.539, 6.099, -100]}
        scale={[13.573, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube009.geometry}
        material={wallMaterial}
        position={[10.213, 6.099, -95.317]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-4.334, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube010.geometry}
        material={wallMaterial}
        position={[19.977, 6.099, -80]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube011.geometry}
        material={wallMaterial}
        position={[19.977, 6.099, -60]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube012.geometry}
        material={wallMaterial}
        position={[19.977, 6.099, -40]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube013.geometry}
        material={wallMaterial}
        position={[19.977, 6.099, -20]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube014.geometry}
        material={wallMaterial}
        position={[19.977, 6.099, 0]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube015.geometry}
        material={wallMaterial}
        position={[19.977, 6.099, 20]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube016.geometry}
        material={wallMaterial}
        position={[19.977, 6.099, 40]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube017.geometry}
        material={wallMaterial}
        position={[19.977, 6.099, 60]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube018.geometry}
        material={wallMaterial}
        position={[-20.195, 6.099, -80]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube020.geometry}
        material={wallMaterial}
        position={[-20.195, 6.099, 80]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube021.geometry}
        material={wallMaterial}
        position={[-20.195, 6.099, 60]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube022.geometry}
        material={wallMaterial}
        position={[-20.195, 6.099, 40]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube023.geometry}
        material={wallMaterial}
        position={[-20.195, 6.099, 20]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube024.geometry}
        material={wallMaterial}
        position={[-20.195, 6.099, 0]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube025.geometry}
        material={wallMaterial}
        position={[-20.195, 6.099, -20]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube026.geometry}
        material={wallMaterial}
        position={[-20.195, 6.099, -40]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube027.geometry}
        material={wallMaterial}
        position={[-20.195, 6.099, -60]}
        scale={[10, 6, 0.235]}
      />
      <mesh
        geometry={nodes.Cube008.geometry}
        material={wallMaterial}
        position={[29.859, 6.099, -0.054]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[100.51, 6, 0.235]}
      />

      {/* Keep the rest of the original meshes with original materials for pillars, floors, etc. */}
      <mesh
        geometry={nodes.Cube029.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, -82.171]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-2.35, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube030.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, -73.341]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-6.626, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube031.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, -45.107]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-5.243, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube032.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, -57.706]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-2.052, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube033.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, -35.052]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-5.243, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube034.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, -22.542]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-2.61, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube035.geometry}
        material={materials["Material.002"]}
        position={[-9.947, 6.099, -15.573]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-4.527, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube036.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, -3.34]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-3.121, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube037.geometry}
        material={materials.pillars}
        position={[10.63, 6.099, 25.058]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube038.geometry}
        material={materials.pillars}
        position={[10.63, 6.099, 35.009]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube039.geometry}
        material={materials.pillars}
        position={[10.63, 6.099, 30.115]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube041.geometry}
        material={materials.pillars}
        position={[-10.815, 6.099, -4.49]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube042.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, 2.964]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-3.121, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube043.geometry}
        material={materials.pillars}
        position={[10.63, 6.099, 13.228]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube045.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, 63.142]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-3.159, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube044.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, 81.847]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-1.757, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube046.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, 46.488]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-6.71, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube047.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, 94.992]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-5.274, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube048.geometry}
        material={materials["Material.002"]}
        position={[10.213, 6.099, 77.005]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-3.159, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube049.geometry}
        material={materials.pillars}
        position={[10.63, 6.099, -16.504]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube050.geometry}
        material={materials["Material.002"]}
        position={[-10.285, 6.099, 20.679]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-4.527, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube051.geometry}
        material={materials["Material.002"]}
        position={[-10.298, 6.099, 4.328]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-4.527, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube052.geometry}
        material={materials["Material.002"]}
        position={[-9.947, 6.099, -22.958]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-4.527, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube053.geometry}
        material={materials["Material.002"]}
        position={[-9.947, 6.099, -37.705]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-4.527, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube054.geometry}
        material={materials.pillars}
        position={[-10.815, 6.099, -50.89]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube055.geometry}
        material={materials.pillars}
        position={[-10.815, 6.099, -83.914]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube056.geometry}
        material={materials["Material.002"]}
        position={[-9.947, 6.099, -62.992]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-5.248, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube057.geometry}
        material={materials["Material.002"]}
        position={[-9.947, 6.099, -77.2]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-2.95, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube058.geometry}
        material={materials["Material.002"]}
        position={[-9.947, 6.099, -94.854]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-5.248, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube059.geometry}
        material={materials["Material.002"]}
        position={[-10.285, 6.099, 37.565]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-4.527, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube060.geometry}
        material={materials.pillars}
        position={[-10.815, 6.099, 55.39]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube061.geometry}
        material={materials.pillars}
        position={[-10.815, 6.099, 48.562]}
        rotation={[0, Math.PI / 2, 0]}
        scale={[0.604, 6, 0.665]}
      />
      <mesh
        geometry={nodes.Cube062.geometry}
        material={materials["Material.002"]}
        position={[-10.285, 6.099, 84.655]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-4.527, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube063.geometry}
        material={materials["Material.002"]}
        position={[-10.285, 6.099, 76.414]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-3.827, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube064.geometry}
        material={materials["Material.002"]}
        position={[-10.285, 6.099, 63.681]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-3.827, -6, -0.235]}
      />
      <mesh
        geometry={nodes.Cube065.geometry}
        material={materials["Material.002"]}
        position={[-10.285, 6.099, 97.116]}
        rotation={[Math.PI, -Math.PI / 2, 0]}
        scale={[-2.997, -6, -0.235]}
      />

      {/* Floor and ceiling planes with textures */}
      <mesh
        geometry={nodes.Plane.geometry}
        material={ceilingMaterial}
        position={[1.445, 12.15, -0.259]}
        scale={[200, 500, 70]}
      />
      <mesh
        geometry={nodes.Plane001.geometry}
        material={floorMaterial}
        position={[-3.284, 0.234, -8.813]}
        scale={[43.453, 122.615, 122.615]}
      />

      {/* Keep original lighting fixtures */}
      <mesh
        geometry={nodes.Cube028.geometry}
        material={nodes.Cube028.material}
        position={[20.624, 12.144, -5.065]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube040.geometry}
        material={nodes.Cube040.material}
        position={[20.624, 12.144, -13.541]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube066.geometry}
        material={nodes.Cube066.material}
        position={[20.624, 12.144, -28.423]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube067.geometry}
        material={nodes.Cube067.material}
        position={[20.624, 12.144, -48.004]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube068.geometry}
        material={nodes.Cube068.material}
        position={[20.624, 12.144, -68.621]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube069.geometry}
        material={nodes.Cube069.material}
        position={[20.624, 12.144, -89.564]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube070.geometry}
        material={nodes.Cube070.material}
        position={[-19.155, 12.144, -89.564]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube071.geometry}
        material={nodes.Cube071.material}
        position={[-19.155, 12.144, -69.903]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube072.geometry}
        material={nodes.Cube072.material}
        position={[-19.155, 12.144, -29.465]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube073.geometry}
        material={nodes.Cube073.material}
        position={[-19.155, 12.144, -6.896]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube074.geometry}
        material={nodes.Cube074.material}
        position={[-19.155, 12.144, -13.661]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube075.geometry}
        material={nodes.Cube075.material}
        position={[-19.155, 12.144, 14.312]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube076.geometry}
        material={nodes.Cube076.material}
        position={[-19.155, 12.144, 5.523]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube077.geometry}
        material={nodes.Cube077.material}
        position={[-19.155, 12.144, 5.523]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube078.geometry}
        material={nodes.Cube078.material}
        position={[-19.155, 12.144, 5.523]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube079.geometry}
        material={nodes.Cube079.material}
        position={[20.063, 12.144, 28.409]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube080.geometry}
        material={nodes.Cube080.material}
        position={[-19.155, 12.144, 49.468]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube081.geometry}
        material={nodes.Cube081.material}
        position={[19.798, 12.144, 49.468]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube082.geometry}
        material={nodes.Cube082.material}
        position={[19.798, 12.144, 69.6]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube083.geometry}
        material={nodes.Cube083.material}
        position={[19.798, 12.144, 69.6]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube084.geometry}
        material={nodes.Cube084.material}
        position={[-19.155, 12.144, 67.845]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube085.geometry}
        material={nodes.Cube085.material}
        position={[-19.155, 12.144, 87.498]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube086.geometry}
        material={nodes.Cube086.material}
        position={[19.798, 12.144, 88.939]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Plane002.geometry}
        material={floorMaterial}
        position={[-3.284, 0.233, -0.259]}
        scale={[43.453, 122.615, 122.615]}
      />
      {/* <mesh
        geometry={nodes.Plane003.geometry}
        material={floorMaterial}
        position={[-3.284, 0.233, -8.813]}
        scale={[43.453, 122.615, 122.615]}
      /> */}

      {/* More lighting fixtures */}
      <mesh
        geometry={nodes.Cube090.geometry}
        material={nodes.Cube090.material}
        position={[-3.001, 12.144, -79.612]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube091.geometry}
        material={nodes.Cube091.material}
        position={[5.141, 12.144, -79.612]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube092.geometry}
        material={nodes.Cube092.material}
        position={[5.141, 12.144, -60.018]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube093.geometry}
        material={nodes.Cube093.material}
        position={[5.141, 12.144, -40.051]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube094.geometry}
        material={nodes.Cube094.material}
        position={[5.141, 12.144, -20.161]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube095.geometry}
        material={nodes.Cube095.material}
        position={[5.141, 12.144, -0.323]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube096.geometry}
        material={nodes.Cube096.material}
        position={[5.141, 12.144, 19.718]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube097.geometry}
        material={nodes.Cube097.material}
        position={[5.141, 12.144, 39.523]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube098.geometry}
        material={nodes.Cube098.material}
        position={[5.141, 12.144, 59.528]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube099.geometry}
        material={nodes.Cube099.material}
        position={[5.141, 12.144, 59.528]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube100.geometry}
        material={nodes.Cube100.material}
        position={[5.141, 12.144, 79.649]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube101.geometry}
        material={nodes.Cube101.material}
        position={[-3.001, 12.144, -79.612]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube102.geometry}
        material={nodes.Cube102.material}
        position={[-3.001, 12.144, -60.244]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube103.geometry}
        material={nodes.Cube103.material}
        position={[-3.001, 12.144, -40.108]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube104.geometry}
        material={nodes.Cube104.material}
        position={[-3.001, 12.144, -20.184]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube105.geometry}
        material={nodes.Cube105.material}
        position={[-3.001, 12.144, -0.274]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube106.geometry}
        material={nodes.Cube106.material}
        position={[-3.001, 12.144, 19.875]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube107.geometry}
        material={nodes.Cube107.material}
        position={[-3.001, 12.144, 39.559]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube108.geometry}
        material={nodes.Cube108.material}
        position={[-3.001, 12.144, 59.737]}
        scale={[0.754, 0.112, 0.907]}
      />
      <mesh
        geometry={nodes.Cube109.geometry}
        material={nodes.Cube109.material}
        position={[-3.001, 12.144, 79.353]}
        scale={[0.754, 0.112, 0.907]}
      />
    </group>
  );
}

useGLTF.preload("/hall3.glb");
